package com.syos.test.model;

import com.syos.model.*;
import com.syos.test.base.BaseTest;
import org.junit.Test;

import java.time.LocalDateTime;

import static org.junit.Assert.*;

/**
 * Unit tests for the Transaction model class.
 */
public class TransactionTest extends BaseTest {
    
    /**
     * Tests the constructor and getters.
     */
    @Test
    public void testConstructorAndGetters() {
        // Create a user
        User user = new User("U001", "testuser", "password", "Test User", 
                "<EMAIL>", "123 Test St", "123-456-7890", true);
        
        // Create a transaction
        Transaction transaction = new Transaction("T001", user, TransactionType.ONLINE);
        
        // Verify the values
        assertEquals("T001", transaction.getTransactionId());
        assertEquals(user, transaction.getUser());
        assertEquals(TransactionType.ONLINE, transaction.getType());
        assertNotNull(transaction.getDateTime());
        assertNotNull(transaction.getItems());
        assertTrue(transaction.getItems().isEmpty());
    }
    
    /**
     * Tests the setters.
     */
    @Test
    public void testSetters() {
        // Create a user
        User user = new User("U001", "testuser", "password", "Test User", 
                "<EMAIL>", "123 Test St", "123-456-7890", true);
        
        // Create a transaction
        Transaction transaction = new Transaction();
        
        // Set values
        transaction.setTransactionId("T001");
        transaction.setUser(user);
        transaction.setType(TransactionType.ONLINE);
        LocalDateTime dateTime = LocalDateTime.now().minusHours(1);
        transaction.setDateTime(dateTime);
        
        // Verify the values
        assertEquals("T001", transaction.getTransactionId());
        assertEquals(user, transaction.getUser());
        assertEquals(TransactionType.ONLINE, transaction.getType());
        assertEquals(dateTime, transaction.getDateTime());
    }
    
    /**
     * Tests adding items to a transaction.
     */
    @Test
    public void testAddItem() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        
        // Create items
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item2 = new Item("I002", "Flour", 1.80, "Groceries");
        
        // Add items
        transaction.addItem(item1, 3);
        transaction.addItem(item2, 2);
        
        // Verify the items
        assertEquals(2, transaction.getItems().size());
        
        CartItem cartItem1 = transaction.getItems().get(0);
        assertEquals(item1, cartItem1.getItem());
        assertEquals(3, cartItem1.getQuantity());
        
        CartItem cartItem2 = transaction.getItems().get(1);
        assertEquals(item2, cartItem2.getItem());
        assertEquals(2, cartItem2.getQuantity());
    }
    
    /**
     * Tests calculating the total amount.
     */
    @Test
    public void testCalculateTotal() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        
        // Create items
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item2 = new Item("I002", "Flour", 1.80, "Groceries");
        
        // Add items
        transaction.addItem(item1, 3); // 3 * 2.50 = 7.50
        transaction.addItem(item2, 2); // 2 * 1.80 = 3.60
        
        // Calculate total
        double total = transaction.calculateTotal();
        
        // Verify the total (7.50 + 3.60 = 11.10)
        assertEquals(11.10, total, 0.001);
        assertEquals(11.10, transaction.getTotalAmount(), 0.001);
    }
    
    /**
     * Tests applying a discount.
     */
    @Test
    public void testApplyDiscount() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        
        // Create items
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item2 = new Item("I002", "Flour", 1.80, "Groceries");
        
        // Add items
        transaction.addItem(item1, 3); // 3 * 2.50 = 7.50
        transaction.addItem(item2, 2); // 2 * 1.80 = 3.60
        
        // Calculate total
        transaction.calculateTotal(); // 11.10
        
        // Apply 10% discount
        transaction.applyDiscount(10);
        
        // Verify the discount and final amount
        assertEquals(1.11, transaction.getDiscountAmount(), 0.001); // 10% of 11.10
        assertEquals(9.99, transaction.getFinalAmount(), 0.001); // 11.10 - 1.11
        
        // Test invalid discount percentage (negative)
        transaction.applyDiscount(-5);
        assertEquals(11.10, transaction.getFinalAmount(), 0.001); // Should reset to total
        
        // Test invalid discount percentage (> 100)
        transaction.applyDiscount(110);
        assertEquals(11.10, transaction.getFinalAmount(), 0.001); // Should reset to total
    }
    
    /**
     * Tests processing payment.
     */
    @Test
    public void testProcessPayment() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        
        // Create items
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        
        // Add items
        transaction.addItem(item1, 4); // 4 * 2.50 = 10.00
        
        // Calculate total
        transaction.calculateTotal(); // 10.00
        
        // Process payment (sufficient)
        double change1 = transaction.processPayment(15.00);
        
        // Verify the payment and change
        assertEquals(15.00, transaction.getCashTendered(), 0.001);
        assertEquals(5.00, transaction.getChange(), 0.001);
        assertEquals(5.00, change1, 0.001);
        
        // Process payment (insufficient)
        double change2 = transaction.processPayment(5.00);
        
        // Verify the payment and change
        assertEquals(-1.0, change2, 0.001); // Should return -1 for insufficient payment
    }
    
    /**
     * Tests the toString method.
     */
    @Test
    public void testToString() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        
        // Create items
        Item item = new Item("I001", "Rice", 2.50, "Groceries");
        
        // Add items
        transaction.addItem(item, 3);
        
        // Calculate total
        transaction.calculateTotal();
        
        // Verify the string representation contains key information
        String str = transaction.toString();
        assertTrue(str.contains("T001"));
        assertTrue(str.contains("IN_STORE"));
        assertTrue(str.contains("7.5")); // 3 * 2.50 = 7.50
    }
}
