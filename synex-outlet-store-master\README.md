# Synex Outlet Store (SYOS)

## Overview

Synex Outlet Store (SYOS) is a comprehensive Java-based grocery store management system designed for small to medium-sized retail businesses. The system provides functionality for inventory management, billing operations, and reporting, all through a user-friendly console interface.

Key features include:
- **Inventory Management**: Track items across shelf, storage, and online inventory with batch-wise stock management
- **Smart Stock Management**: Automatic stock rotation based on expiry dates
- **Billing System**: Process in-store and online transactions with discount support
- **User Management**: Register and authenticate users for online purchases
- **Comprehensive Reporting**: Generate sales, inventory, and transaction reports

This system was developed as part of a Clean Coding & Concurrent Programming module, focusing on implementing OOP principles and clean code practices.

## Prerequisites

To run the SYOS system, you need:

- **Java Development Kit (JDK)** 8 or higher
- **JUnit 4** (for running tests)
- **Mockito** (for tests that use mocking)

## Environment Setup

### Directory Structure

The project follows this directory structure:
```
SYOS/
├── com/                  # Source code
│   └── syos/             # Main package
├── bin/                  # Compiled class files
├── lib/                  # External libraries
│   ├── junit-4.13.2.jar
│   ├── hamcrest-core-1.3.jar
│   ├── mockito-core-3.12.4.jar
│   └── byte-buddy-1.11.13.jar
├── data/                 # Data storage directory (created on first run)
├── reports/              # Generated reports directory (created on first run)
├── run.bat               # Windows execution script
├── run.sh                # Unix execution script
└── README.md             # This file
```

### Setting Up Libraries

1. Create the necessary directories:
   ```bash
   mkdir -p bin lib data reports
   ```

2. Download the required libraries:
   ```bash
   # JUnit
   curl -L https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar -o lib/junit-4.13.2.jar
   
   # Hamcrest (required by JUnit)
   curl -L https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar -o lib/hamcrest-core-1.3.jar
   
   # Mockito
   curl -L https://repo1.maven.org/maven2/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar -o lib/mockito-core-3.12.4.jar
   
   # ByteBuddy (required by Mockito)
   curl -L https://repo1.maven.org/maven2/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar -o lib/byte-buddy-1.11.13.jar
   ```

## Compilation and Execution

### Windows

1. **Compile the code**:
   ```batch
   mkdir bin
   javac -d bin com\syos\model\*.java com\syos\repository\*.java com\syos\repository\impl\*.java com\syos\service\*.java com\syos\util\*.java com\syos\ui\*.java com\syos\*.java
   ```

2. **Run the application**:
   ```batch
   java -cp bin com.syos.SyosApplication
   ```

   Alternatively, use the provided batch file:
   ```batch
   run.bat
   ```

### Unix-like Systems (Linux, macOS)

1. **Compile the code**:
   ```bash
   mkdir -p bin
   javac -d bin com/syos/model/*.java com/syos/repository/*.java com/syos/repository/impl/*.java com/syos/service/*.java com/syos/util/*.java com/syos/ui/*.java com/syos/*.java
   ```

2. **Run the application**:
   ```bash
   java -cp bin com.syos.SyosApplication
   ```

   Alternatively, use the provided shell script:
   ```bash
   chmod +x run.sh
   ./run.sh
   ```

## Running the Test Suite

### Compiling Tests

```bash
# Windows
javac -d bin -cp "bin;lib\junit-4.13.2.jar;lib\mockito-core-3.12.4.jar;lib\hamcrest-core-1.3.jar;lib\byte-buddy-1.11.13.jar" com\syos\test\util\*.java com\syos\test\base\*.java com\syos\test\model\*.java com\syos\test\repository\*.java com\syos\test\service\*.java com\syos\test\integration\*.java com\syos\test\*.java

# Unix-like systems
javac -d bin -cp "bin:lib/junit-4.13.2.jar:lib/mockito-core-3.12.4.jar:lib/hamcrest-core-1.3.jar:lib/byte-buddy-1.11.13.jar" com/syos/test/util/*.java com/syos/test/base/*.java com/syos/test/model/*.java com/syos/test/repository/*.java com/syos/test/service/*.java com/syos/test/integration/*.java com/syos/test/*.java
```

### Running All Tests

```bash
# Windows
java -cp "bin;lib\junit-4.13.2.jar;lib\mockito-core-3.12.4.jar;lib\hamcrest-core-1.3.jar;lib\byte-buddy-1.11.13.jar" org.junit.runner.JUnitCore com.syos.test.SyosTestSuite

# Unix-like systems
java -cp "bin:lib/junit-4.13.2.jar:lib/mockito-core-3.12.4.jar:lib/hamcrest-core-1.3.jar:lib/byte-buddy-1.11.13.jar" org.junit.runner.JUnitCore com.syos.test.SyosTestSuite
```

### Running Individual Tests

```bash
# Example: Running just the ItemTest
java -cp "bin:lib/junit-4.13.2.jar:lib/hamcrest-core-1.3.jar" org.junit.runner.JUnitCore com.syos.test.model.ItemTest
```

## Using the Application

### Main Menu

When you start the application, you'll see the main menu with these options:
```
===== SYNEX OUTLET STORE (SYOS) =====
1. Billing Operations
2. Inventory Management
3. Reports
4. User Management
5. Exit
```

### Inventory Management

Select option 2 from the main menu to access inventory management:

#### Adding a New Item
1. Select "1. Add New Item"
2. Enter the item details:
   - Item code (e.g., "I001")
   - Item name (e.g., "Rice")
   - Item price (e.g., "2.50")
   - Item category (e.g., "Groceries")

#### Adding Stock
1. Select "2. Add Stock Batch"
2. Enter the batch details:
   - Item code (must be an existing item)
   - Batch ID (e.g., "SB001")
   - Quantity (e.g., "100")
   - Received date (format: yyyy-MM-dd)
   - Expiry date (format: yyyy-MM-dd)
   - Stock type (1 for Shelf, 2 for Storage, 3 for Online)

#### Viewing Stock
1. Select "3. View All Items" to see all items
2. Select "4. View Item Stock" to see stock for a specific item

#### Moving Items to Shelf
1. Select "5. Move Items from Storage to Shelf"
2. Enter the item code and quantity to move

### Billing Operations

Select option 1 from the main menu to access billing operations:

#### Processing In-Store Transactions
1. Select "1. New In-Store Transaction"
2. Add items to the cart:
   - Enter the item code
   - Enter the quantity
   - Repeat for additional items
   - Enter "done" when finished
3. Enter a discount percentage if applicable
4. Enter the cash tendered
5. The system will calculate change and generate a bill

#### Processing Online Transactions
1. First, register and log in as an online customer (see User Management)
2. Select "2. New Online Transaction"
3. Follow the same steps as for in-store transactions

### Reporting

Select option 3 from the main menu to access reports:

#### Available Reports
1. **Daily Sales Report**: Shows sales by item for a specific date
2. **Reshelf Report**: Shows items that need to be moved from storage to shelf
3. **Reorder Report**: Shows items with stock below 50 units
4. **Full Stock Report**: Shows all stock batches with details
5. **Bill Report**: Shows all customer transactions

### User Management

Select option 4 from the main menu to access user management:

#### Registering a New User
1. Select "1. Register New User"
2. Enter the user details:
   - User ID (e.g., "U001")
   - Username
   - Password
   - Full name
   - Email
   - Address
   - Phone
   - Whether this is an online customer (y/n)

#### Logging In
1. Select "2. Login"
2. Enter your username and password

## Troubleshooting

### Common Issues

1. **ClassNotFoundException**
   - Ensure your classpath includes all necessary libraries and the bin directory
   - Check that all Java files have been compiled

2. **File Permissions**
   - Ensure the application has write permissions to the data and reports directories
   - On Unix-like systems, you may need to run `chmod -R 755 data reports`

3. **Data Files Not Found**
   - The application will create necessary data files on first run
   - If you encounter issues, check if the data directory exists and is writable

4. **Test Failures**
   - Check that you're using compatible versions of JUnit and Mockito
   - Ensure the test data directory is writable

## Data Storage Structure

The SYOS system uses CSV files for data persistence:

- **data/items.csv**: Stores item information (code, name, price, category)
- **data/stock.csv**: Stores stock batch information (batch ID, item code, quantity, dates, type)
- **data/users.csv**: Stores user information (ID, username, password, etc.)
- **data/transactions.csv**: Stores transaction headers
- **data/transaction_items.csv**: Stores items in each transaction
- **data/bills.csv**: Stores bill information
- **data/bills/**: Directory containing individual bill text files

## Credits and Acknowledgments

This project was developed as part of the Clean Coding & Concurrent Programming module. It implements principles from:

- "Clean Code" by Robert C. Martin
- Object-Oriented Programming principles
- Software design patterns

Special thanks to:
- The JUnit team for their testing framework
- The Mockito team for their mocking framework

---

© 2025 Synex Outlet Store (SYOS). All rights reserved.
