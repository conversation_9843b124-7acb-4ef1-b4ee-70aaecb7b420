package com.syos.repository;

import com.syos.model.Bill;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Bill data operations.
 */
public interface BillRepository {
    
    /**
     * Saves a bill to the repository.
     * 
     * @param bill The bill to save
     * @return The saved bill
     */
    Bill save(Bill bill);
    
    /**
     * Finds a bill by its number.
     * 
     * @param billNumber The bill number
     * @return An Optional containing the bill if found, empty otherwise
     */
    Optional<Bill> findByNumber(String billNumber);
    
    /**
     * Finds all bills for a specific transaction.
     * 
     * @param transactionId The transaction ID
     * @return A list of bills for the transaction
     */
    List<Bill> findByTransactionId(String transactionId);
    
    /**
     * Finds all bills generated on a specific date.
     * 
     * @param date The date
     * @return A list of bills generated on the specified date
     */
    List<Bill> findByDate(LocalDate date);
    
    /**
     * Finds all bills in the repository.
     * 
     * @return A list of all bills
     */
    List<Bill> findAll();
    
    /**
     * Deletes a bill from the repository.
     * 
     * @param billNumber The number of the bill to delete
     * @return true if deleted, false otherwise
     */
    boolean delete(String billNumber);
    
    /**
     * Gets the next available bill number.
     * 
     * @return The next bill number
     */
    String getNextBillNumber();
}
