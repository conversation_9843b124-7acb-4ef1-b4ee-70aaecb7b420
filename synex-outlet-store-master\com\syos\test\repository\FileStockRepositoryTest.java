package com.syos.test.repository;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.repository.ItemRepository;
import com.syos.repository.StockRepository;
import com.syos.repository.impl.FileItemRepository;
import com.syos.repository.impl.FileStockRepository;
import com.syos.test.base.BaseTest;
import com.syos.test.util.TestDataUtil;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;

/**
 * Unit tests for the FileStockRepository class.
 * Tests the CRUD operations and data persistence.
 */
public class FileStockRepositoryTest extends BaseTest {
    
    private ItemRepository itemRepository;
    private StockRepository stockRepository;
    private Item testItem;
    
    /**
     * Sets up the test environment.
     * Creates repository instances and test data.
     */
    @Before
    public void setUpRepository() {
        // Override the data directory for testing
        System.setProperty("data.directory", TestDataUtil.TEST_DATA_DIR);
        itemRepository = new FileItemRepository();
        stockRepository = new FileStockRepository();
        
        // Create and save a test item
        testItem = TestDataUtil.createTestItem("I001");
        itemRepository.save(testItem);
    }
    
    /**
     * Tests saving a stock batch.
     */
    @Test
    public void testSave() {
        // Create a test stock batch
        StockBatch batch = createTestBatch("SB001", StockType.SHELF);
        
        // Save the batch
        StockBatch savedBatch = stockRepository.save(batch);
        
        // Verify the saved batch
        assertNotNull(savedBatch);
        assertEquals(batch.getBatchId(), savedBatch.getBatchId());
        assertEquals(batch.getItem().getCode(), savedBatch.getItem().getCode());
        assertEquals(batch.getQuantity(), savedBatch.getQuantity());
        assertEquals(batch.getStockType(), savedBatch.getStockType());
        
        // Verify the batch was persisted
        Optional<StockBatch> retrievedBatch = stockRepository.findByBatchId("SB001");
        assertTrue(retrievedBatch.isPresent());
        assertEquals(batch.getQuantity(), retrievedBatch.get().getQuantity());
    }
    
    /**
     * Tests finding a stock batch by ID.
     */
    @Test
    public void testFindByBatchId() {
        // Create and save test batches
        StockBatch batch1 = createTestBatch("SB001", StockType.SHELF);
        StockBatch batch2 = createTestBatch("SB002", StockType.STORAGE);
        
        stockRepository.save(batch1);
        stockRepository.save(batch2);
        
        // Find batches by ID
        Optional<StockBatch> foundBatch1 = stockRepository.findByBatchId("SB001");
        Optional<StockBatch> foundBatch2 = stockRepository.findByBatchId("SB002");
        Optional<StockBatch> notFoundBatch = stockRepository.findByBatchId("SB999");
        
        // Verify the results
        assertTrue(foundBatch1.isPresent());
        assertEquals("SB001", foundBatch1.get().getBatchId());
        assertEquals(StockType.SHELF, foundBatch1.get().getStockType());
        
        assertTrue(foundBatch2.isPresent());
        assertEquals("SB002", foundBatch2.get().getBatchId());
        assertEquals(StockType.STORAGE, foundBatch2.get().getStockType());
        
        assertFalse(notFoundBatch.isPresent());
    }
    
    /**
     * Tests finding stock batches by item code.
     */
    @Test
    public void testFindByItemCode() {
        // Create and save test batches for the same item
        StockBatch batch1 = createTestBatch("SB001", StockType.SHELF);
        StockBatch batch2 = createTestBatch("SB002", StockType.STORAGE);
        
        stockRepository.save(batch1);
        stockRepository.save(batch2);
        
        // Find batches by item code
        List<StockBatch> batches = stockRepository.findByItemCode("I001");
        
        // Verify the results
        assertNotNull(batches);
        assertEquals(2, batches.size());
        
        // Verify the batches are for the correct item
        for (StockBatch batch : batches) {
            assertEquals("I001", batch.getItem().getCode());
        }
        
        // Find batches for a non-existent item
        List<StockBatch> noBatches = stockRepository.findByItemCode("I999");
        assertTrue(noBatches.isEmpty());
    }
    
    /**
     * Tests finding stock batches by stock type.
     */
    @Test
    public void testFindByStockType() {
        // Create and save test batches with different stock types
        StockBatch shelfBatch1 = createTestBatch("SB001", StockType.SHELF);
        StockBatch shelfBatch2 = createTestBatch("SB002", StockType.SHELF);
        StockBatch storageBatch = createTestBatch("SB003", StockType.STORAGE);
        StockBatch onlineBatch = createTestBatch("SB004", StockType.ONLINE);
        
        stockRepository.save(shelfBatch1);
        stockRepository.save(shelfBatch2);
        stockRepository.save(storageBatch);
        stockRepository.save(onlineBatch);
        
        // Find batches by stock type
        List<StockBatch> shelfBatches = stockRepository.findByStockType(StockType.SHELF);
        List<StockBatch> storageBatches = stockRepository.findByStockType(StockType.STORAGE);
        List<StockBatch> onlineBatches = stockRepository.findByStockType(StockType.ONLINE);
        
        // Verify the results
        assertEquals(2, shelfBatches.size());
        assertEquals(1, storageBatches.size());
        assertEquals(1, onlineBatches.size());
        
        // Verify the batches have the correct stock type
        for (StockBatch batch : shelfBatches) {
            assertEquals(StockType.SHELF, batch.getStockType());
        }
        
        for (StockBatch batch : storageBatches) {
            assertEquals(StockType.STORAGE, batch.getStockType());
        }
        
        for (StockBatch batch : onlineBatches) {
            assertEquals(StockType.ONLINE, batch.getStockType());
        }
    }
    
    /**
     * Tests updating a stock batch.
     */
    @Test
    public void testUpdate() {
        // Create and save a test batch
        StockBatch batch = createTestBatch("SB001", StockType.SHELF);
        stockRepository.save(batch);
        
        // Update the batch
        batch.setQuantity(50);
        batch.setStockType(StockType.STORAGE);
        
        StockBatch updatedBatch = stockRepository.update(batch);
        
        // Verify the updated batch
        assertNotNull(updatedBatch);
        assertEquals(50, updatedBatch.getQuantity());
        assertEquals(StockType.STORAGE, updatedBatch.getStockType());
        
        // Verify the batch was persisted
        Optional<StockBatch> retrievedBatch = stockRepository.findByBatchId("SB001");
        assertTrue(retrievedBatch.isPresent());
        assertEquals(50, retrievedBatch.get().getQuantity());
        assertEquals(StockType.STORAGE, retrievedBatch.get().getStockType());
    }
    
    /**
     * Tests deleting a stock batch.
     */
    @Test
    public void testDelete() {
        // Create and save test batches
        StockBatch batch1 = createTestBatch("SB001", StockType.SHELF);
        StockBatch batch2 = createTestBatch("SB002", StockType.STORAGE);
        
        stockRepository.save(batch1);
        stockRepository.save(batch2);
        
        // Verify batches exist
        assertTrue(stockRepository.findByBatchId("SB001").isPresent());
        assertTrue(stockRepository.findByBatchId("SB002").isPresent());
        
        // Delete a batch
        boolean deleted = stockRepository.delete("SB001");
        
        // Verify the result
        assertTrue(deleted);
        assertFalse(stockRepository.findByBatchId("SB001").isPresent());
        assertTrue(stockRepository.findByBatchId("SB002").isPresent());
        
        // Try to delete a non-existent batch
        boolean notDeleted = stockRepository.delete("SB999");
        assertFalse(notDeleted);
    }
    
    /**
     * Tests getting the total quantity of an item by stock type.
     */
    @Test
    public void testGetTotalQuantity() {
        // Create and save test batches for the same item but different stock types
        StockBatch shelfBatch1 = createTestBatch("SB001", StockType.SHELF, 30);
        StockBatch shelfBatch2 = createTestBatch("SB002", StockType.SHELF, 20);
        StockBatch storageBatch = createTestBatch("SB003", StockType.STORAGE, 50);
        
        stockRepository.save(shelfBatch1);
        stockRepository.save(shelfBatch2);
        stockRepository.save(storageBatch);
        
        // Get total quantities
        int shelfQuantity = stockRepository.getTotalQuantity("I001", StockType.SHELF);
        int storageQuantity = stockRepository.getTotalQuantity("I001", StockType.STORAGE);
        int onlineQuantity = stockRepository.getTotalQuantity("I001", StockType.ONLINE);
        
        // Verify the results
        assertEquals(50, shelfQuantity); // 30 + 20
        assertEquals(50, storageQuantity);
        assertEquals(0, onlineQuantity); // No online batches
    }
    
    /**
     * Creates a test stock batch.
     * 
     * @param batchId The batch ID
     * @param stockType The stock type
     * @return A test stock batch
     */
    private StockBatch createTestBatch(String batchId, StockType stockType) {
        return createTestBatch(batchId, stockType, 100);
    }
    
    /**
     * Creates a test stock batch with a specific quantity.
     * 
     * @param batchId The batch ID
     * @param stockType The stock type
     * @param quantity The quantity
     * @return A test stock batch
     */
    private StockBatch createTestBatch(String batchId, StockType stockType, int quantity) {
        LocalDate today = LocalDate.now();
        return new StockBatch(
                batchId,
                testItem,
                quantity,
                today.minusDays(10),
                today.plusMonths(6),
                stockType
        );
    }
}
