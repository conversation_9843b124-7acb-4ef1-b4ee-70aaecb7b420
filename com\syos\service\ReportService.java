package com.syos.service;

import com.syos.model.*;
import com.syos.repository.BillRepository;
import com.syos.repository.ItemRepository;
import com.syos.repository.StockRepository;
import com.syos.repository.TransactionRepository;
import com.syos.util.DateUtil;
import com.syos.util.FileUtil;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service class for generating reports.
 */
public class ReportService {
    
    private final ItemRepository itemRepository;
    private final StockRepository stockRepository;
    private final TransactionRepository transactionRepository;
    private final BillRepository billRepository;
    private final InventoryService inventoryService;
    
    private static final String REPORTS_DIRECTORY = "reports";
    
    public ReportService(ItemRepository itemRepository,
                         StockRepository stockRepository,
                         TransactionRepository transactionRepository,
                         BillRepository billRepository,
                         InventoryService inventoryService) {
        this.itemRepository = itemRepository;
        this.stockRepository = stockRepository;
        this.transactionRepository = transactionRepository;
        this.billRepository = billRepository;
        this.inventoryService = inventoryService;
        
        // Ensure reports directory exists
        FileUtil.ensureDirectoryExists(REPORTS_DIRECTORY);
    }
    
    /**
     * Generates a daily sales report.
     * 
     * @param date The date for the report
     * @return The path to the generated report file
     */
    public String generateDailySalesReport(LocalDate date) {
        List<Transaction> transactions = transactionRepository.findByDate(date);
        
        // Calculate sales per item
        Map<String, Integer> quantitySold = new HashMap<>();
        Map<String, Double> revenue = new HashMap<>();
        
        for (Transaction transaction : transactions) {
            for (CartItem cartItem : transaction.getItems()) {
                String itemCode = cartItem.getItem().getCode();
                int quantity = cartItem.getQuantity();
                double subtotal = cartItem.getSubtotal();
                
                quantitySold.put(itemCode, quantitySold.getOrDefault(itemCode, 0) + quantity);
                revenue.put(itemCode, revenue.getOrDefault(itemCode, 0.0) + subtotal);
            }
        }
        
        // Generate report content
        StringBuilder report = new StringBuilder();
        report.append("Daily Sales Report - ").append(DateUtil.formatDate(date)).append("\n");
        report.append("==========================================================\n");
        report.append(String.format("%-10s %-20s %-10s %-10s\n", "Code", "Name", "Quantity", "Revenue"));
        report.append("----------------------------------------------------------\n");
        
        double totalRevenue = 0.0;
        
        for (String itemCode : quantitySold.keySet()) {
            itemRepository.findByCode(itemCode).ifPresent(item -> {
                report.append(String.format("%-10s %-20s %-10d $%-9.2f\n",
                        item.getCode(),
                        item.getName(),
                        quantitySold.get(itemCode),
                        revenue.get(itemCode)));
            });
            
            totalRevenue += revenue.get(itemCode);
        }
        
        report.append("----------------------------------------------------------\n");
        report.append(String.format("%-41s $%-9.2f\n", "Total Revenue:", totalRevenue));
        report.append("==========================================================\n");
        
        // Save report to file
        String fileName = REPORTS_DIRECTORY + "/daily_sales_" + DateUtil.formatDate(date) + ".txt";
        FileUtil.writeLinesToFile(fileName, List.of(report.toString()));
        
        return fileName;
    }
    
    /**
     * Generates a report of items to be reshelved.
     * 
     * @return The path to the generated report file
     */
    public String generateReshelfReport() {
        List<Item> items = itemRepository.findAll();
        
        StringBuilder report = new StringBuilder();
        report.append("Items to be Reshelved Report - ").append(DateUtil.formatDate(LocalDate.now())).append("\n");
        report.append("==========================================================\n");
        report.append(String.format("%-10s %-20s %-10s\n", "Code", "Name", "Quantity"));
        report.append("----------------------------------------------------------\n");
        
        int totalToReshelf = 0;
        
        for (Item item : items) {
            int shelfQuantity = inventoryService.getTotalQuantity(item.getCode(), StockType.SHELF);
            int storageQuantity = inventoryService.getTotalQuantity(item.getCode(), StockType.STORAGE);
            
            // If shelf quantity is low but storage has stock, suggest reshelving
            if (shelfQuantity < 10 && storageQuantity > 0) {
                int quantityToReshelf = Math.min(storageQuantity, 20 - shelfQuantity);
                
                report.append(String.format("%-10s %-20s %-10d\n",
                        item.getCode(),
                        item.getName(),
                        quantityToReshelf));
                
                totalToReshelf += quantityToReshelf;
            }
        }
        
        report.append("----------------------------------------------------------\n");
        report.append(String.format("%-31s %-10d\n", "Total Items to Reshelf:", totalToReshelf));
        report.append("==========================================================\n");
        
        // Save report to file
        String fileName = REPORTS_DIRECTORY + "/reshelf_" + DateUtil.formatDate(LocalDate.now()) + ".txt";
        FileUtil.writeLinesToFile(fileName, List.of(report.toString()));
        
        return fileName;
    }
    
    /**
     * Generates a report of items to be reordered (stock < 50).
     * 
     * @return The path to the generated report file
     */
    public String generateReorderReport() {
        List<Item> itemsToReorder = inventoryService.getItemsToReorder();
        
        StringBuilder report = new StringBuilder();
        report.append("Items to Reorder Report - ").append(DateUtil.formatDate(LocalDate.now())).append("\n");
        report.append("==========================================================\n");
        report.append(String.format("%-10s %-20s %-10s %-10s\n", "Code", "Name", "Current", "Reorder"));
        report.append("----------------------------------------------------------\n");
        
        for (Item item : itemsToReorder) {
            int totalQuantity = inventoryService.getTotalQuantity(item.getCode(), StockType.SHELF)
                    + inventoryService.getTotalQuantity(item.getCode(), StockType.STORAGE)
                    + inventoryService.getTotalQuantity(item.getCode(), StockType.ONLINE);
            
            int reorderQuantity = 100 - totalQuantity; // Reorder to reach 100 items
            
            report.append(String.format("%-10s %-20s %-10d %-10d\n",
                    item.getCode(),
                    item.getName(),
                    totalQuantity,
                    reorderQuantity));
        }
        
        report.append("==========================================================\n");
        
        // Save report to file
        String fileName = REPORTS_DIRECTORY + "/reorder_" + DateUtil.formatDate(LocalDate.now()) + ".txt";
        FileUtil.writeLinesToFile(fileName, List.of(report.toString()));
        
        return fileName;
    }
    
    /**
     * Generates a full stock report.
     * 
     * @return The path to the generated report file
     */
    public String generateFullStockReport() {
        List<StockBatch> allBatches = stockRepository.findAll();
        
        StringBuilder report = new StringBuilder();
        report.append("Full Stock Report - ").append(DateUtil.formatDate(LocalDate.now())).append("\n");
        report.append("==========================================================\n");
        report.append(String.format("%-10s %-10s %-20s %-10s %-12s %-12s %-10s\n",
                "Batch ID", "Code", "Name", "Quantity", "Received", "Expiry", "Type"));
        report.append("----------------------------------------------------------\n");
        
        for (StockBatch batch : allBatches) {
            report.append(String.format("%-10s %-10s %-20s %-10d %-12s %-12s %-10s\n",
                    batch.getBatchId(),
                    batch.getItem().getCode(),
                    batch.getItem().getName(),
                    batch.getQuantity(),
                    DateUtil.formatDate(batch.getReceivedDate()),
                    DateUtil.formatDate(batch.getExpiryDate()),
                    batch.getStockType()));
        }
        
        report.append("==========================================================\n");
        
        // Save report to file
        String fileName = REPORTS_DIRECTORY + "/full_stock_" + DateUtil.formatDate(LocalDate.now()) + ".txt";
        FileUtil.writeLinesToFile(fileName, List.of(report.toString()));
        
        return fileName;
    }
    
    /**
     * Generates a bill report.
     * 
     * @return The path to the generated report file
     */
    public String generateBillReport() {
        List<Bill> allBills = billRepository.findAll();
        
        StringBuilder report = new StringBuilder();
        report.append("Bill Report - ").append(DateUtil.formatDate(LocalDate.now())).append("\n");
        report.append("==========================================================\n");
        report.append(String.format("%-15s %-20s %-10s %-10s %-10s\n",
                "Bill Number", "Date", "Items", "Amount", "Type"));
        report.append("----------------------------------------------------------\n");
        
        double totalAmount = 0.0;
        
        for (Bill bill : allBills) {
            Transaction transaction = bill.getTransaction();
            report.append(String.format("%-15s %-20s %-10d $%-9.2f %-10s\n",
                    bill.getBillNumber(),
                    DateUtil.formatDateTime(bill.getGeneratedDateTime()),
                    transaction.getItems().size(),
                    transaction.getFinalAmount(),
                    transaction.getType()));
            
            totalAmount += transaction.getFinalAmount();
        }
        
        report.append("----------------------------------------------------------\n");
        report.append(String.format("%-46s $%-9.2f\n", "Total Amount:", totalAmount));
        report.append("==========================================================\n");
        
        // Save report to file
        String fileName = REPORTS_DIRECTORY + "/bill_report_" + DateUtil.formatDate(LocalDate.now()) + ".txt";
        FileUtil.writeLinesToFile(fileName, List.of(report.toString()));
        
        return fileName;
    }
}
