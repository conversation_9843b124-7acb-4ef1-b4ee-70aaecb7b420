package com.syos.test.service;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.repository.ItemRepository;
import com.syos.repository.StockRepository;
import com.syos.service.InventoryService;
import com.syos.test.base.BaseTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for the InventoryService class.
 * Uses <PERSON>ckito to mock the repository dependencies.
 */
public class InventoryServiceTest extends BaseTest {
    
    @Mock
    private ItemRepository itemRepository;
    
    @Mock
    private StockRepository stockRepository;
    
    private InventoryService inventoryService;
    
    /**
     * Sets up the test environment.
     * Initializes mocks and creates an InventoryService instance.
     */
    @Before
    public void setUpService() {
        MockitoAnnotations.initMocks(this);
        inventoryService = new InventoryService(itemRepository, stockRepository);
    }
    
    /**
     * Tests adding an item.
     */
    @Test
    public void testAddItem() {
        // Create a test item
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        
        // Mock the repository behavior
        when(itemRepository.save(item)).thenReturn(item);
        
        // Call the service method
        Item result = inventoryService.addItem(item);
        
        // Verify the result
        assertNotNull(result);
        assertEquals(item, result);
        
        // Verify the repository was called
        verify(itemRepository).save(item);
    }
    
    /**
     * Tests finding an item by code.
     */
    @Test
    public void testFindItemByCode() {
        // Create a test item
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        
        // Mock the repository behavior
        when(itemRepository.findByCode("I001")).thenReturn(Optional.of(item));
        when(itemRepository.findByCode("I999")).thenReturn(Optional.empty());
        
        // Call the service method
        Optional<Item> foundItem = inventoryService.findItemByCode("I001");
        Optional<Item> notFoundItem = inventoryService.findItemByCode("I999");
        
        // Verify the results
        assertTrue(foundItem.isPresent());
        assertEquals(item, foundItem.get());
        assertFalse(notFoundItem.isPresent());
        
        // Verify the repository was called
        verify(itemRepository).findByCode("I001");
        verify(itemRepository).findByCode("I999");
    }
    
    /**
     * Tests getting all items.
     */
    @Test
    public void testGetAllItems() {
        // Create test items
        List<Item> items = Arrays.asList(
                new Item("I001", "Item 1", 10.0, "Test"),
                new Item("I002", "Item 2", 20.0, "Test")
        );
        
        // Mock the repository behavior
        when(itemRepository.findAll()).thenReturn(items);
        
        // Call the service method
        List<Item> result = inventoryService.getAllItems();
        
        // Verify the result
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(items, result);
        
        // Verify the repository was called
        verify(itemRepository).findAll();
    }
    
    /**
     * Tests adding a stock batch.
     */
    @Test
    public void testAddStockBatch() {
        // Create a test item and batch
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        StockBatch batch = new StockBatch(
                "SB001",
                item,
                100,
                LocalDate.now().minusDays(10),
                LocalDate.now().plusMonths(6),
                StockType.SHELF
        );
        
        // Mock the repository behavior
        when(stockRepository.save(batch)).thenReturn(batch);
        
        // Call the service method
        StockBatch result = inventoryService.addStockBatch(batch);
        
        // Verify the result
        assertNotNull(result);
        assertEquals(batch, result);
        
        // Verify the repository was called
        verify(stockRepository).save(batch);
    }
    
    /**
     * Tests checking if there is sufficient stock.
     */
    @Test
    public void testHasSufficientStock() {
        // Mock the repository behavior
        when(stockRepository.getTotalQuantity("I001", StockType.SHELF)).thenReturn(100);
        when(stockRepository.getTotalQuantity("I001", StockType.STORAGE)).thenReturn(50);
        
        // Call the service method
        boolean hasSufficientShelf1 = inventoryService.hasSufficientStock("I001", 50, StockType.SHELF);
        boolean hasSufficientShelf2 = inventoryService.hasSufficientStock("I001", 150, StockType.SHELF);
        boolean hasSufficientStorage1 = inventoryService.hasSufficientStock("I001", 50, StockType.STORAGE);
        boolean hasSufficientStorage2 = inventoryService.hasSufficientStock("I001", 60, StockType.STORAGE);
        
        // Verify the results
        assertTrue(hasSufficientShelf1);
        assertFalse(hasSufficientShelf2);
        assertTrue(hasSufficientStorage1);
        assertFalse(hasSufficientStorage2);
        
        // Verify the repository was called
        verify(stockRepository, times(2)).getTotalQuantity("I001", StockType.SHELF);
        verify(stockRepository, times(2)).getTotalQuantity("I001", StockType.STORAGE);
    }
    
    /**
     * Tests reducing stock.
     */
    @Test
    public void testReduceStock() {
        // Create test items and batches
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        
        StockBatch batch1 = new StockBatch(
                "SB001",
                item,
                30,
                LocalDate.now().minusDays(20),
                LocalDate.now().plusMonths(3),
                StockType.SHELF
        );
        
        StockBatch batch2 = new StockBatch(
                "SB002",
                item,
                70,
                LocalDate.now().minusDays(10),
                LocalDate.now().plusMonths(6),
                StockType.SHELF
        );
        
        List<StockBatch> batches = Arrays.asList(batch1, batch2);
        
        // Mock the repository behavior
        when(stockRepository.getTotalQuantity("I001", StockType.SHELF)).thenReturn(100);
        when(stockRepository.findByItemCode("I001")).thenReturn(batches);
        when(stockRepository.update(any(StockBatch.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // Call the service method
        boolean result = inventoryService.reduceStock("I001", 50, StockType.SHELF);
        
        // Verify the result
        assertTrue(result);
        
        // Verify the batches were updated correctly
        assertEquals(0, batch1.getQuantity()); // First batch should be depleted (30 items)
        assertEquals(50, batch2.getQuantity()); // Second batch should have 20 items removed
        
        // Verify the repository was called
        verify(stockRepository).getTotalQuantity("I001", StockType.SHELF);
        verify(stockRepository).findByItemCode("I001");
        verify(stockRepository, times(2)).update(any(StockBatch.class));
    }
    
    /**
     * Tests moving items from storage to shelf.
     */
    @Test
    public void testMoveFromStorageToShelf() {
        // Create test items and batches
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        
        LocalDate today = LocalDate.now();
        LocalDate expiryDate1 = today.plusMonths(3);
        LocalDate expiryDate2 = today.plusMonths(6);
        
        StockBatch storageBatch1 = new StockBatch(
                "ST001",
                item,
                30,
                today.minusDays(20),
                expiryDate1,
                StockType.STORAGE
        );
        
        StockBatch storageBatch2 = new StockBatch(
                "ST002",
                item,
                70,
                today.minusDays(10),
                expiryDate2,
                StockType.STORAGE
        );
        
        StockBatch shelfBatch = new StockBatch(
                "SB001",
                item,
                20,
                today.minusDays(15),
                expiryDate1,
                StockType.SHELF
        );
        
        List<StockBatch> allBatches = new ArrayList<>();
        allBatches.add(storageBatch1);
        allBatches.add(storageBatch2);
        allBatches.add(shelfBatch);
        
        // Mock the repository behavior
        when(stockRepository.getTotalQuantity("I001", StockType.STORAGE)).thenReturn(100);
        when(stockRepository.findByItemCode("I001")).thenReturn(allBatches);
        when(stockRepository.update(any(StockBatch.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(stockRepository.save(any(StockBatch.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // Call the service method
        boolean result = inventoryService.moveFromStorageToShelf("I001", 40);
        
        // Verify the result
        assertTrue(result);
        
        // Verify the batches were updated correctly
        assertEquals(0, storageBatch1.getQuantity()); // First batch should be depleted (30 items)
        assertEquals(60, storageBatch2.getQuantity()); // Second batch should have 10 items removed
        assertEquals(50, shelfBatch.getQuantity()); // Shelf batch should have 30 items added
        
        // Verify the repository was called
        verify(stockRepository).getTotalQuantity("I001", StockType.STORAGE);
        verify(stockRepository, times(2)).findByItemCode("I001");
        verify(stockRepository, times(2)).update(any(StockBatch.class));
    }
    
    /**
     * Tests getting items to reorder.
     */
    @Test
    public void testGetItemsToReorder() {
        // Create test items
        Item item1 = new Item("I001", "Item 1", 10.0, "Test");
        Item item2 = new Item("I002", "Item 2", 20.0, "Test");
        Item item3 = new Item("I003", "Item 3", 30.0, "Test");
        
        List<Item> allItems = Arrays.asList(item1, item2, item3);
        
        // Mock the repository behavior
        when(itemRepository.findAll()).thenReturn(allItems);
        
        // Mock the stock quantities
        when(stockRepository.getTotalQuantity("I001", StockType.SHELF)).thenReturn(20);
        when(stockRepository.getTotalQuantity("I001", StockType.STORAGE)).thenReturn(20);
        when(stockRepository.getTotalQuantity("I001", StockType.ONLINE)).thenReturn(10);
        
        when(stockRepository.getTotalQuantity("I002", StockType.SHELF)).thenReturn(10);
        when(stockRepository.getTotalQuantity("I002", StockType.STORAGE)).thenReturn(10);
        when(stockRepository.getTotalQuantity("I002", StockType.ONLINE)).thenReturn(5);
        
        when(stockRepository.getTotalQuantity("I003", StockType.SHELF)).thenReturn(30);
        when(stockRepository.getTotalQuantity("I003", StockType.STORAGE)).thenReturn(30);
        when(stockRepository.getTotalQuantity("I003", StockType.ONLINE)).thenReturn(20);
        
        // Call the service method
        List<Item> itemsToReorder = inventoryService.getItemsToReorder();
        
        // Verify the result
        assertNotNull(itemsToReorder);
        assertEquals(1, itemsToReorder.size());
        assertEquals(item2, itemsToReorder.get(0)); // Only item2 has total quantity < 50
        
        // Verify the repository was called
        verify(itemRepository).findAll();
        verify(stockRepository, times(3)).getTotalQuantity("I001", any(StockType.class));
        verify(stockRepository, times(3)).getTotalQuantity("I002", any(StockType.class));
        verify(stockRepository, times(3)).getTotalQuantity("I003", any(StockType.class));
    }
}
