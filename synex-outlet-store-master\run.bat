@echo off
echo Compiling SYOS application...
mkdir bin 2>nul
mkdir data 2>nul
mkdir reports 2>nul
javac -d bin com\syos\model\*.java com\syos\repository\*.java com\syos\repository\impl\*.java com\syos\service\*.java com\syos\util\*.java com\syos\ui\*.java com\syos\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    exit /b %ERRORLEVEL%
)

echo Starting SYOS application...
java -cp bin com.syos.SyosApplication
