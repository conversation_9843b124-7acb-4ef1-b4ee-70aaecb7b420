package com.syos.test.service;

import com.syos.model.*;
import com.syos.repository.BillRepository;
import com.syos.repository.ItemRepository;
import com.syos.repository.StockRepository;
import com.syos.repository.TransactionRepository;
import com.syos.service.InventoryService;
import com.syos.service.ReportService;
import com.syos.test.base.BaseTest;
import com.syos.util.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for the ReportService class.
 * Uses Mockito to mock the repository and service dependencies.
 */
public class ReportServiceTest extends BaseTest {
    
    @Mock
    private ItemRepository itemRepository;
    
    @Mock
    private StockRepository stockRepository;
    
    @Mock
    private TransactionRepository transactionRepository;
    
    @Mock
    private BillRepository billRepository;
    
    @Mock
    private InventoryService inventoryService;
    
    private ReportService reportService;
    
    /**
     * Sets up the test environment.
     * Initializes mocks and creates a ReportService instance.
     */
    @Before
    public void setUpService() {
        MockitoAnnotations.initMocks(this);
        reportService = new ReportService(
                itemRepository, stockRepository, transactionRepository, billRepository, inventoryService);
    }
    
    /**
     * Tests generating a daily sales report.
     */
    @Test
    public void testGenerateDailySalesReport() {
        // Create test data
        LocalDate date = LocalDate.now();
        
        Item item1 = new Item("I001", "Item 1", 10.0, "Test");
        Item item2 = new Item("I002", "Item 2", 20.0, "Test");
        
        Transaction transaction1 = new Transaction("T001", null, TransactionType.IN_STORE);
        transaction1.setDateTime(LocalDateTime.of(date.getYear(), date.getMonth(), date.getDayOfMonth(), 10, 0));
        transaction1.addItem(item1, 2); // 2 * 10.0 = 20.0
        transaction1.addItem(item2, 1); // 1 * 20.0 = 20.0
        transaction1.calculateTotal(); // 40.0
        
        Transaction transaction2 = new Transaction("T002", null, TransactionType.IN_STORE);
        transaction2.setDateTime(LocalDateTime.of(date.getYear(), date.getMonth(), date.getDayOfMonth(), 14, 0));
        transaction2.addItem(item1, 3); // 3 * 10.0 = 30.0
        transaction2.calculateTotal(); // 30.0
        
        List<Transaction> transactions = Arrays.asList(transaction1, transaction2);
        
        // Mock the repository behavior
        when(transactionRepository.findByDate(date)).thenReturn(transactions);
        when(itemRepository.findByCode("I001")).thenReturn(java.util.Optional.of(item1));
        when(itemRepository.findByCode("I002")).thenReturn(java.util.Optional.of(item2));
        
        // Call the service method
        String reportPath = reportService.generateDailySalesReport(date);
        
        // Verify the result
        assertNotNull(reportPath);
        assertTrue(reportPath.contains("daily_sales_"));
        assertTrue(reportPath.contains(DateUtil.formatDate(date)));
        
        // Verify the repository was called
        verify(transactionRepository).findByDate(date);
        verify(itemRepository).findByCode("I001");
        verify(itemRepository).findByCode("I002");
    }
    
    /**
     * Tests generating a reshelf report.
     */
    @Test
    public void testGenerateReshelfReport() {
        // Create test data
        List<Item> items = Arrays.asList(
                new Item("I001", "Item 1", 10.0, "Test"),
                new Item("I002", "Item 2", 20.0, "Test"),
                new Item("I003", "Item 3", 30.0, "Test")
        );
        
        // Mock the repository behavior
        when(itemRepository.findAll()).thenReturn(items);
        
        // Mock the inventory service behavior
        when(inventoryService.getTotalQuantity("I001", StockType.SHELF)).thenReturn(5); // Low shelf stock
        when(inventoryService.getTotalQuantity("I001", StockType.STORAGE)).thenReturn(20); // Has storage stock
        
        when(inventoryService.getTotalQuantity("I002", StockType.SHELF)).thenReturn(15); // Sufficient shelf stock
        when(inventoryService.getTotalQuantity("I002", StockType.STORAGE)).thenReturn(30);
        
        when(inventoryService.getTotalQuantity("I003", StockType.SHELF)).thenReturn(3); // Low shelf stock
        when(inventoryService.getTotalQuantity("I003", StockType.STORAGE)).thenReturn(0); // No storage stock
        
        // Call the service method
        String reportPath = reportService.generateReshelfReport();
        
        // Verify the result
        assertNotNull(reportPath);
        assertTrue(reportPath.contains("reshelf_"));
        
        // Verify the repository was called
        verify(itemRepository).findAll();
        verify(inventoryService, times(3)).getTotalQuantity(anyString(), eq(StockType.SHELF));
        verify(inventoryService, times(3)).getTotalQuantity(anyString(), eq(StockType.STORAGE));
    }
    
    /**
     * Tests generating a reorder report.
     */
    @Test
    public void testGenerateReorderReport() {
        // Create test data
        List<Item> itemsToReorder = Arrays.asList(
                new Item("I001", "Item 1", 10.0, "Test"),
                new Item("I002", "Item 2", 20.0, "Test")
        );
        
        // Mock the inventory service behavior
        when(inventoryService.getItemsToReorder()).thenReturn(itemsToReorder);
        
        // Call the service method
        String reportPath = reportService.generateReorderReport();
        
        // Verify the result
        assertNotNull(reportPath);
        assertTrue(reportPath.contains("reorder_"));
        
        // Verify the service was called
        verify(inventoryService).getItemsToReorder();
    }
    
    /**
     * Tests generating a full stock report.
     */
    @Test
    public void testGenerateFullStockReport() {
        // Create test data
        Item item1 = new Item("I001", "Item 1", 10.0, "Test");
        Item item2 = new Item("I002", "Item 2", 20.0, "Test");
        
        LocalDate today = LocalDate.now();
        
        StockBatch batch1 = new StockBatch(
                "SB001",
                item1,
                50,
                today.minusDays(10),
                today.plusMonths(6),
                StockType.SHELF
        );
        
        StockBatch batch2 = new StockBatch(
                "SB002",
                item2,
                30,
                today.minusDays(5),
                today.plusMonths(3),
                StockType.STORAGE
        );
        
        List<StockBatch> batches = Arrays.asList(batch1, batch2);
        
        // Mock the repository behavior
        when(stockRepository.findAll()).thenReturn(batches);
        
        // Call the service method
        String reportPath = reportService.generateFullStockReport();
        
        // Verify the result
        assertNotNull(reportPath);
        assertTrue(reportPath.contains("full_stock_"));
        
        // Verify the repository was called
        verify(stockRepository).findAll();
    }
    
    /**
     * Tests generating a bill report.
     */
    @Test
    public void testGenerateBillReport() {
        // Create test data
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        
        Transaction transaction1 = new Transaction("T001", null, TransactionType.IN_STORE);
        transaction1.addItem(item, 2);
        transaction1.calculateTotal();
        
        Transaction transaction2 = new Transaction("T002", null, TransactionType.ONLINE);
        transaction2.addItem(item, 3);
        transaction2.calculateTotal();
        
        Bill bill1 = new Bill("B001", transaction1);
        Bill bill2 = new Bill("B002", transaction2);
        
        List<Bill> bills = Arrays.asList(bill1, bill2);
        
        // Mock the repository behavior
        when(billRepository.findAll()).thenReturn(bills);
        
        // Call the service method
        String reportPath = reportService.generateBillReport();
        
        // Verify the result
        assertNotNull(reportPath);
        assertTrue(reportPath.contains("bill_report_"));
        
        // Verify the repository was called
        verify(billRepository).findAll();
    }
}
