package com.syos.test.integration;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.repository.ItemRepository;
import com.syos.repository.StockRepository;
import com.syos.repository.impl.FileItemRepository;
import com.syos.repository.impl.FileStockRepository;
import com.syos.service.InventoryService;
import com.syos.test.base.BaseTest;
import com.syos.test.util.TestDataUtil;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;

/**
 * Integration test for the inventory management workflow.
 * Tests the complete process of adding items, managing stock,
 * and moving items between storage and shelf.
 */
public class InventoryWorkflowTest extends BaseTest {
    
    private ItemRepository itemRepository;
    private StockRepository stockRepository;
    private InventoryService inventoryService;
    
    /**
     * Sets up the test environment.
     * Initializes repositories and services.
     */
    @Before
    public void setUpWorkflow() {
        // Override the data directory for testing
        System.setProperty("data.directory", TestDataUtil.TEST_DATA_DIR);
        
        // Initialize repositories
        itemRepository = new FileItemRepository();
        stockRepository = new FileStockRepository();
        
        // Initialize service
        inventoryService = new InventoryService(itemRepository, stockRepository);
    }
    
    /**
     * Tests the complete inventory management workflow.
     * This test verifies the process of adding items, adding stock batches,
     * and moving items from storage to shelf.
     */
    @Test
    public void testInventoryManagementWorkflow() {
        // 1. Add items
        Item rice = new Item("I001", "Rice", 2.50, "Groceries");
        Item flour = new Item("I002", "Flour", 1.80, "Groceries");
        
        Item savedRice = inventoryService.addItem(rice);
        Item savedFlour = inventoryService.addItem(flour);
        
        assertNotNull(savedRice);
        assertNotNull(savedFlour);
        assertEquals("I001", savedRice.getCode());
        assertEquals("I002", savedFlour.getCode());
        
        // 2. Verify items were saved
        Optional<Item> retrievedRice = inventoryService.findItemByCode("I001");
        Optional<Item> retrievedFlour = inventoryService.findItemByCode("I002");
        
        assertTrue(retrievedRice.isPresent());
        assertTrue(retrievedFlour.isPresent());
        assertEquals("Rice", retrievedRice.get().getName());
        assertEquals("Flour", retrievedFlour.get().getName());
        
        // 3. Add stock batches
        LocalDate today = LocalDate.now();
        
        // Storage batches
        StockBatch riceStorageBatch1 = new StockBatch(
                "ST001",
                rice,
                50,
                today.minusDays(20),
                today.plusMonths(3),
                StockType.STORAGE
        );
        
        StockBatch riceStorageBatch2 = new StockBatch(
                "ST002",
                rice,
                70,
                today.minusDays(10),
                today.plusMonths(6),
                StockType.STORAGE
        );
        
        StockBatch flourStorageBatch = new StockBatch(
                "ST003",
                flour,
                60,
                today.minusDays(15),
                today.plusMonths(4),
                StockType.STORAGE
        );
        
        inventoryService.addStockBatch(riceStorageBatch1);
        inventoryService.addStockBatch(riceStorageBatch2);
        inventoryService.addStockBatch(flourStorageBatch);
        
        // Shelf batches
        StockBatch riceShelfBatch = new StockBatch(
                "SB001",
                rice,
                20,
                today.minusDays(5),
                today.plusMonths(3),
                StockType.SHELF
        );
        
        StockBatch flourShelfBatch = new StockBatch(
                "SB002",
                flour,
                15,
                today.minusDays(3),
                today.plusMonths(4),
                StockType.SHELF
        );
        
        inventoryService.addStockBatch(riceShelfBatch);
        inventoryService.addStockBatch(flourShelfBatch);
        
        // 4. Verify stock batches were saved
        List<StockBatch> riceBatches = inventoryService.getStockBatchesByItemCode("I001");
        List<StockBatch> flourBatches = inventoryService.getStockBatchesByItemCode("I002");
        
        assertEquals(3, riceBatches.size());
        assertEquals(2, flourBatches.size());
        
        // 5. Check total quantities
        int riceShelfQuantity = inventoryService.getTotalQuantity("I001", StockType.SHELF);
        int riceStorageQuantity = inventoryService.getTotalQuantity("I001", StockType.STORAGE);
        int flourShelfQuantity = inventoryService.getTotalQuantity("I002", StockType.SHELF);
        int flourStorageQuantity = inventoryService.getTotalQuantity("I002", StockType.STORAGE);
        
        assertEquals(20, riceShelfQuantity);
        assertEquals(120, riceStorageQuantity); // 50 + 70
        assertEquals(15, flourShelfQuantity);
        assertEquals(60, flourStorageQuantity);
        
        // 6. Move items from storage to shelf
        boolean riceMoved = inventoryService.moveFromStorageToShelf("I001", 30);
        boolean flourMoved = inventoryService.moveFromStorageToShelf("I002", 20);
        
        assertTrue(riceMoved);
        assertTrue(flourMoved);
        
        // 7. Verify quantities after moving
        int newRiceShelfQuantity = inventoryService.getTotalQuantity("I001", StockType.SHELF);
        int newRiceStorageQuantity = inventoryService.getTotalQuantity("I001", StockType.STORAGE);
        int newFlourShelfQuantity = inventoryService.getTotalQuantity("I002", StockType.SHELF);
        int newFlourStorageQuantity = inventoryService.getTotalQuantity("I002", StockType.STORAGE);
        
        assertEquals(50, newRiceShelfQuantity); // 20 + 30
        assertEquals(90, newRiceStorageQuantity); // 120 - 30
        assertEquals(35, newFlourShelfQuantity); // 15 + 20
        assertEquals(40, newFlourStorageQuantity); // 60 - 20
        
        // 8. Verify the batches were updated correctly
        List<StockBatch> updatedRiceBatches = inventoryService.getStockBatchesByItemCode("I001");
        
        // The oldest batch with closest expiry date should be used first
        Optional<StockBatch> oldestRiceStorageBatch = updatedRiceBatches.stream()
                .filter(batch -> batch.getBatchId().equals("ST001"))
                .findFirst();
        
        assertTrue(oldestRiceStorageBatch.isPresent());
        assertEquals(20, oldestRiceStorageBatch.get().getQuantity()); // 50 - 30
    }
    
    /**
     * Tests the expiry date prioritization when moving items from storage to shelf.
     * This test verifies that items with closer expiry dates are moved first,
     * even if they are not from the oldest batch.
     */
    @Test
    public void testExpiryDatePrioritization() {
        // 1. Add an item
        Item milk = new Item("I003", "Milk", 1.20, "Dairy");
        inventoryService.addItem(milk);
        
        // 2. Add storage batches with different expiry dates
        LocalDate today = LocalDate.now();
        
        // Older batch with later expiry
        StockBatch olderBatch = new StockBatch(
                "ST004",
                milk,
                50,
                today.minusDays(30), // Received 30 days ago
                today.plusMonths(6), // Expires in 6 months
                StockType.STORAGE
        );
        
        // Newer batch with earlier expiry
        StockBatch newerBatch = new StockBatch(
                "ST005",
                milk,
                30,
                today.minusDays(10), // Received 10 days ago
                today.plusMonths(1), // Expires in 1 month
                StockType.STORAGE
        );
        
        inventoryService.addStockBatch(olderBatch);
        inventoryService.addStockBatch(newerBatch);
        
        // 3. Move items from storage to shelf
        boolean moved = inventoryService.moveFromStorageToShelf("I003", 20);
        assertTrue(moved);
        
        // 4. Verify the newer batch with earlier expiry was used first
        List<StockBatch> milkBatches = inventoryService.getStockBatchesByItemCode("I003");
        
        Optional<StockBatch> updatedOlderBatch = milkBatches.stream()
                .filter(batch -> batch.getBatchId().equals("ST004"))
                .findFirst();
        
        Optional<StockBatch> updatedNewerBatch = milkBatches.stream()
                .filter(batch -> batch.getBatchId().equals("ST005"))
                .findFirst();
        
        assertTrue(updatedOlderBatch.isPresent());
        assertTrue(updatedNewerBatch.isPresent());
        
        assertEquals(50, updatedOlderBatch.get().getQuantity()); // Unchanged
        assertEquals(10, updatedNewerBatch.get().getQuantity()); // 30 - 20
        
        // 5. Verify a new shelf batch was created with the correct expiry date
        Optional<StockBatch> shelfBatch = milkBatches.stream()
                .filter(batch -> batch.getStockType() == StockType.SHELF)
                .findFirst();
        
        assertTrue(shelfBatch.isPresent());
        assertEquals(20, shelfBatch.get().getQuantity());
        assertEquals(today.plusMonths(1), shelfBatch.get().getExpiryDate()); // Same as the newer batch
    }
}
