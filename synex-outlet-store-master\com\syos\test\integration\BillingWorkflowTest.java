package com.syos.test.integration;

import com.syos.model.*;
import com.syos.repository.*;
import com.syos.repository.impl.*;
import com.syos.service.BillingService;
import com.syos.service.InventoryService;
import com.syos.test.base.BaseTest;
import com.syos.test.util.TestDataUtil;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;
import java.util.Optional;

import static org.junit.Assert.*;

/**
 * Integration test for the billing workflow.
 * Tests the complete process of creating a transaction, adding items,
 * calculating totals, processing payment, and generating a bill.
 */
public class BillingWorkflowTest extends BaseTest {
    
    private ItemRepository itemRepository;
    private StockRepository stockRepository;
    private TransactionRepository transactionRepository;
    private BillRepository billRepository;
    private InventoryService inventoryService;
    private BillingService billingService;
    
    /**
     * Sets up the test environment.
     * Initializes repositories and services, and creates test data.
     */
    @Before
    public void setUpWorkflow() {
        // Override the data directory for testing
        System.setProperty("data.directory", TestDataUtil.TEST_DATA_DIR);
        
        // Initialize repositories
        itemRepository = new FileItemRepository();
        stockRepository = new FileStockRepository();
        transactionRepository = new FileTransactionRepository();
        billRepository = new FileBillRepository();
        
        // Initialize services
        inventoryService = new InventoryService(itemRepository, stockRepository);
        billingService = new BillingService(itemRepository, transactionRepository, billRepository, inventoryService);
        
        // Create test data
        createTestData();
    }
    
    /**
     * Creates test data for the workflow.
     */
    private void createTestData() {
        // Create test items
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item2 = new Item("I002", "Flour", 1.80, "Groceries");
        Item item3 = new Item("I003", "Sugar", 1.50, "Groceries");
        
        itemRepository.save(item1);
        itemRepository.save(item2);
        itemRepository.save(item3);
        
        // Create test stock batches
        LocalDate today = LocalDate.now();
        
        // Shelf stock
        StockBatch shelfBatch1 = new StockBatch(
                "SB001",
                item1,
                50,
                today.minusDays(10),
                today.plusMonths(6),
                StockType.SHELF
        );
        
        StockBatch shelfBatch2 = new StockBatch(
                "SB002",
                item2,
                40,
                today.minusDays(8),
                today.plusMonths(5),
                StockType.SHELF
        );
        
        StockBatch shelfBatch3 = new StockBatch(
                "SB003",
                item3,
                30,
                today.minusDays(6),
                today.plusMonths(4),
                StockType.SHELF
        );
        
        stockRepository.save(shelfBatch1);
        stockRepository.save(shelfBatch2);
        stockRepository.save(shelfBatch3);
        
        // Storage stock
        StockBatch storageBatch1 = new StockBatch(
                "ST001",
                item1,
                100,
                today.minusDays(15),
                today.plusMonths(8),
                StockType.STORAGE
        );
        
        StockBatch storageBatch2 = new StockBatch(
                "ST002",
                item2,
                80,
                today.minusDays(12),
                today.plusMonths(7),
                StockType.STORAGE
        );
        
        StockBatch storageBatch3 = new StockBatch(
                "ST003",
                item3,
                60,
                today.minusDays(9),
                today.plusMonths(6),
                StockType.STORAGE
        );
        
        stockRepository.save(storageBatch1);
        stockRepository.save(storageBatch2);
        stockRepository.save(storageBatch3);
        
        // Online stock
        StockBatch onlineBatch1 = new StockBatch(
                "ON001",
                item1,
                30,
                today.minusDays(7),
                today.plusMonths(5),
                StockType.ONLINE
        );
        
        StockBatch onlineBatch2 = new StockBatch(
                "ON002",
                item2,
                25,
                today.minusDays(5),
                today.plusMonths(4),
                StockType.ONLINE
        );
        
        StockBatch onlineBatch3 = new StockBatch(
                "ON003",
                item3,
                20,
                today.minusDays(3),
                today.plusMonths(3),
                StockType.ONLINE
        );
        
        stockRepository.save(onlineBatch1);
        stockRepository.save(onlineBatch2);
        stockRepository.save(onlineBatch3);
    }
    
    /**
     * Tests the complete in-store billing workflow.
     * This test verifies the entire process from creating a transaction to generating a bill,
     * including stock reduction.
     */
    @Test
    public void testInStoreBillingWorkflow() {
        // Get initial stock quantities
        int initialRiceQuantity = inventoryService.getTotalQuantity("I001", StockType.SHELF);
        int initialFlourQuantity = inventoryService.getTotalQuantity("I002", StockType.SHELF);
        
        // 1. Create a transaction
        Transaction transaction = billingService.createTransaction(null, TransactionType.IN_STORE);
        assertNotNull(transaction);
        assertEquals(TransactionType.IN_STORE, transaction.getType());
        
        // 2. Add items to the transaction
        boolean added1 = billingService.addItemToTransaction(transaction, "I001", 5); // 5 kg of rice
        boolean added2 = billingService.addItemToTransaction(transaction, "I002", 3); // 3 kg of flour
        
        assertTrue(added1);
        assertTrue(added2);
        assertEquals(2, transaction.getItems().size());
        
        // 3. Calculate the total
        double total = billingService.calculateTotal(transaction);
        assertEquals(17.90, total, 0.001); // 5 * 2.50 + 3 * 1.80 = 12.50 + 5.40 = 17.90
        
        // 4. Apply a discount
        billingService.applyDiscount(transaction, 10);
        assertEquals(1.79, transaction.getDiscountAmount(), 0.001); // 10% of 17.90
        assertEquals(16.11, transaction.getFinalAmount(), 0.001); // 17.90 - 1.79
        
        // 5. Process payment
        double change = billingService.processPayment(transaction, 20.00);
        assertEquals(3.89, change, 0.001); // 20.00 - 16.11
        
        // 6. Complete the transaction and generate a bill
        Bill bill = billingService.completeTransaction(transaction);
        
        assertNotNull(bill);
        assertNotNull(bill.getBillNumber());
        assertEquals(transaction, bill.getTransaction());
        
        // 7. Verify the bill was saved
        Optional<Bill> savedBill = billRepository.findByNumber(bill.getBillNumber());
        assertTrue(savedBill.isPresent());
        
        // 8. Verify the transaction was saved
        Optional<Transaction> savedTransaction = transactionRepository.findById(transaction.getTransactionId());
        assertTrue(savedTransaction.isPresent());
        
        // 9. Verify the stock was reduced
        int finalRiceQuantity = inventoryService.getTotalQuantity("I001", StockType.SHELF);
        int finalFlourQuantity = inventoryService.getTotalQuantity("I002", StockType.SHELF);
        
        assertEquals(initialRiceQuantity - 5, finalRiceQuantity);
        assertEquals(initialFlourQuantity - 3, finalFlourQuantity);
    }
    
    /**
     * Tests the complete online billing workflow.
     * This test verifies the entire process for an online transaction,
     * including using the online stock.
     */
    @Test
    public void testOnlineBillingWorkflow() {
        // Create a test user
        User user = new User(
                "U001",
                "testuser",
                "password",
                "Test User",
                "<EMAIL>",
                "123 Test St",
                "123-456-7890",
                true
        );
        
        // Get initial stock quantities
        int initialRiceQuantity = inventoryService.getTotalQuantity("I001", StockType.ONLINE);
        int initialSugarQuantity = inventoryService.getTotalQuantity("I003", StockType.ONLINE);
        
        // 1. Create a transaction
        Transaction transaction = billingService.createTransaction(user, TransactionType.ONLINE);
        assertNotNull(transaction);
        assertEquals(TransactionType.ONLINE, transaction.getType());
        assertEquals(user, transaction.getUser());
        
        // 2. Add items to the transaction
        boolean added1 = billingService.addItemToTransaction(transaction, "I001", 2); // 2 kg of rice
        boolean added2 = billingService.addItemToTransaction(transaction, "I003", 4); // 4 kg of sugar
        
        assertTrue(added1);
        assertTrue(added2);
        assertEquals(2, transaction.getItems().size());
        
        // 3. Calculate the total
        double total = billingService.calculateTotal(transaction);
        assertEquals(11.00, total, 0.001); // 2 * 2.50 + 4 * 1.50 = 5.00 + 6.00 = 11.00
        
        // 4. Apply a discount
        billingService.applyDiscount(transaction, 5);
        assertEquals(0.55, transaction.getDiscountAmount(), 0.001); // 5% of 11.00
        assertEquals(10.45, transaction.getFinalAmount(), 0.001); // 11.00 - 0.55
        
        // 5. Process payment (online payment is the exact amount)
        double change = billingService.processPayment(transaction, 10.45);
        assertEquals(0.0, change, 0.001);
        
        // 6. Complete the transaction and generate a bill
        Bill bill = billingService.completeTransaction(transaction);
        
        assertNotNull(bill);
        assertNotNull(bill.getBillNumber());
        assertEquals(transaction, bill.getTransaction());
        
        // 7. Verify the bill was saved
        Optional<Bill> savedBill = billRepository.findByNumber(bill.getBillNumber());
        assertTrue(savedBill.isPresent());
        
        // 8. Verify the transaction was saved
        Optional<Transaction> savedTransaction = transactionRepository.findById(transaction.getTransactionId());
        assertTrue(savedTransaction.isPresent());
        
        // 9. Verify the stock was reduced from the ONLINE stock
        int finalRiceQuantity = inventoryService.getTotalQuantity("I001", StockType.ONLINE);
        int finalSugarQuantity = inventoryService.getTotalQuantity("I003", StockType.ONLINE);
        
        assertEquals(initialRiceQuantity - 2, finalRiceQuantity);
        assertEquals(initialSugarQuantity - 4, finalSugarQuantity);
    }
}
