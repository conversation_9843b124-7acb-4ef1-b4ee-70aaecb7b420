package com.syos.test;

import com.syos.test.integration.BillingWorkflowTest;
import com.syos.test.integration.InventoryWorkflowTest;
import com.syos.test.integration.ReportGenerationTest;
import com.syos.test.model.*;
import com.syos.test.repository.FileItemRepositoryTest;
import com.syos.test.repository.FileStockRepositoryTest;
import com.syos.test.service.*;
import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * Test suite for the SYOS system.
 * Runs all unit and integration tests.
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
        // Model tests
        ItemTest.class,
        StockBatchTest.class,
        TransactionTest.class,
        BillTest.class,
        
        // Repository tests
        FileItemRepositoryTest.class,
        FileStockRepositoryTest.class,
        
        // Service tests
        InventoryServiceTest.class,
        BillingServiceTest.class,
        ReportServiceTest.class,
        UserServiceTest.class,
        
        // Integration tests
        BillingWorkflowTest.class,
        InventoryWorkflowTest.class,
        ReportGenerationTest.class
})
public class SyosTestSuite {
    // This class remains empty, it is used only as a holder for the above annotations
}
