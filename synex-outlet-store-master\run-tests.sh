#!/bin/bash

echo "Setting up test environment..."
mkdir -p bin data reports lib

# Download required libraries if they don't exist
if [ ! -f "lib/junit-4.13.2.jar" ]; then
    echo "Downloading JUnit..."
    curl -L https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar -o lib/junit-4.13.2.jar
fi

if [ ! -f "lib/hamcrest-core-1.3.jar" ]; then
    echo "Downloading Hamcrest..."
    curl -L https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar -o lib/hamcrest-core-1.3.jar
fi

if [ ! -f "lib/mockito-core-3.12.4.jar" ]; then
    echo "Downloading Mockito..."
    curl -L https://repo1.maven.org/maven2/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar -o lib/mockito-core-3.12.4.jar
fi

if [ ! -f "lib/byte-buddy-1.11.13.jar" ]; then
    echo "Downloading ByteBuddy..."
    curl -L https://repo1.maven.org/maven2/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar -o lib/byte-buddy-1.11.13.jar
fi

echo "Compiling main classes..."
javac -d bin com/syos/model/*.java com/syos/repository/*.java com/syos/repository/impl/*.java com/syos/service/*.java com/syos/util/*.java com/syos/ui/*.java com/syos/*.java

if [ $? -ne 0 ]; then
    echo "Main class compilation failed!"
    exit 1
fi

echo "Compiling test classes..."
javac -d bin -cp "bin:lib/junit-4.13.2.jar:lib/mockito-core-3.12.4.jar:lib/hamcrest-core-1.3.jar:lib/byte-buddy-1.11.13.jar" com/syos/test/util/*.java com/syos/test/base/*.java com/syos/test/model/*.java com/syos/test/repository/*.java com/syos/test/service/*.java com/syos/test/integration/*.java com/syos/test/*.java

if [ $? -ne 0 ]; then
    echo "Test class compilation failed!"
    exit 1
fi

echo "Running tests..."
java -cp "bin:lib/junit-4.13.2.jar:lib/mockito-core-3.12.4.jar:lib/hamcrest-core-1.3.jar:lib/byte-buddy-1.11.13.jar" org.junit.runner.JUnitCore com.syos.test.SyosTestSuite
