package com.syos.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a transaction in the system.
 * Contains information about the items purchased, total amount, and payment details.
 */
public class Transaction {
    private String transactionId;
    private LocalDateTime dateTime;
    private List<CartItem> items;
    private User user; // null for in-store transactions without registered users
    private double totalAmount;
    private double discountAmount;
    private double finalAmount;
    private double cashTendered;
    private double change;
    private TransactionType type;
    
    public Transaction() {
        this.items = new ArrayList<>();
        this.dateTime = LocalDateTime.now();
    }
    
    public Transaction(String transactionId, User user, TransactionType type) {
        this.transactionId = transactionId;
        this.user = user;
        this.type = type;
        this.items = new ArrayList<>();
        this.dateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public String getTransactionId() {
        return transactionId;
    }
    
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
    
    public LocalDateTime getDateTime() {
        return dateTime;
    }
    
    public void setDateTime(LocalDateTime dateTime) {
        this.dateTime = dateTime;
    }
    
    public List<CartItem> getItems() {
        return items;
    }
    
    public void setItems(List<CartItem> items) {
        this.items = items;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public double getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public double getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public double getFinalAmount() {
        return finalAmount;
    }
    
    public void setFinalAmount(double finalAmount) {
        this.finalAmount = finalAmount;
    }
    
    public double getCashTendered() {
        return cashTendered;
    }
    
    public void setCashTendered(double cashTendered) {
        this.cashTendered = cashTendered;
    }
    
    public double getChange() {
        return change;
    }
    
    public void setChange(double change) {
        this.change = change;
    }
    
    public TransactionType getType() {
        return type;
    }
    
    public void setType(TransactionType type) {
        this.type = type;
    }
    
    /**
     * Adds an item to the transaction.
     * 
     * @param item The item to add
     * @param quantity The quantity of the item
     */
    public void addItem(Item item, int quantity) {
        CartItem cartItem = new CartItem(item, quantity);
        items.add(cartItem);
    }
    
    /**
     * Calculates the total amount for all items in the transaction.
     * 
     * @return The total amount
     */
    public double calculateTotal() {
        totalAmount = items.stream()
                .mapToDouble(CartItem::getSubtotal)
                .sum();
        return totalAmount;
    }
    
    /**
     * Applies a discount to the transaction.
     * 
     * @param discountPercentage The discount percentage (0-100)
     */
    public void applyDiscount(double discountPercentage) {
        if (discountPercentage > 0 && discountPercentage <= 100) {
            discountAmount = totalAmount * (discountPercentage / 100);
            finalAmount = totalAmount - discountAmount;
        } else {
            finalAmount = totalAmount;
        }
    }
    
    /**
     * Processes payment for the transaction.
     * 
     * @param cashTendered The amount of cash provided
     * @return The change to be returned
     */
    public double processPayment(double cashTendered) {
        if (cashTendered < finalAmount) {
            return -1; // Insufficient payment
        }
        
        this.cashTendered = cashTendered;
        this.change = cashTendered - finalAmount;
        return this.change;
    }
    
    @Override
    public String toString() {
        return "Transaction{" +
                "transactionId='" + transactionId + '\'' +
                ", dateTime=" + dateTime +
                ", items=" + items +
                ", user=" + user +
                ", totalAmount=" + totalAmount +
                ", discountAmount=" + discountAmount +
                ", finalAmount=" + finalAmount +
                ", cashTendered=" + cashTendered +
                ", change=" + change +
                ", type=" + type +
                '}';
    }
}
