package com.syos.ui;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.service.InventoryService;
import com.syos.util.DateUtil;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Scanner;

/**
 * Menu for inventory management operations.
 */
public class InventoryMenu {
    
    private final Scanner scanner;
    private final InventoryService inventoryService;
    
    public InventoryMenu(Scanner scanner, InventoryService inventoryService) {
        this.scanner = scanner;
        this.inventoryService = inventoryService;
    }
    
    /**
     * Displays the inventory menu and handles user input.
     */
    public void displayMenu() {
        boolean back = false;
        
        while (!back) {
            System.out.println("\n===== Inventory Management =====");
            System.out.println("1. Add New Item");
            System.out.println("2. Add Stock Batch");
            System.out.println("3. View All Items");
            System.out.println("4. View Item Stock");
            System.out.println("5. Move Items from Storage to Shelf");
            System.out.println("6. Back to Main Menu");
            System.out.print("Enter your choice: ");
            
            int choice;
            try {
                choice = Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Invalid input. Please enter a number.");
                continue;
            }
            
            switch (choice) {
                case 1:
                    addNewItem();
                    break;
                case 2:
                    addStockBatch();
                    break;
                case 3:
                    viewAllItems();
                    break;
                case 4:
                    viewItemStock();
                    break;
                case 5:
                    moveItemsToShelf();
                    break;
                case 6:
                    back = true;
                    break;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        }
    }
    
    /**
     * Adds a new item to the inventory.
     */
    private void addNewItem() {
        System.out.println("\n===== Add New Item =====");
        
        System.out.print("Enter item code: ");
        String code = scanner.nextLine().trim();
        
        // Check if item code already exists
        if (inventoryService.findItemByCode(code).isPresent()) {
            System.out.println("Item with code " + code + " already exists.");
            return;
        }
        
        System.out.print("Enter item name: ");
        String name = scanner.nextLine().trim();
        
        System.out.print("Enter item price: $");
        double price;
        try {
            price = Double.parseDouble(scanner.nextLine().trim());
            if (price <= 0) {
                System.out.println("Price must be positive. Item not added.");
                return;
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid price. Item not added.");
            return;
        }
        
        System.out.print("Enter item category: ");
        String category = scanner.nextLine().trim();
        
        Item item = new Item(code, name, price, category);
        inventoryService.addItem(item);
        
        System.out.println("Item added successfully.");
    }
    
    /**
     * Adds a new stock batch.
     */
    private void addStockBatch() {
        System.out.println("\n===== Add Stock Batch =====");
        
        System.out.print("Enter item code: ");
        String itemCode = scanner.nextLine().trim();
        
        Optional<Item> itemOpt = inventoryService.findItemByCode(itemCode);
        if (!itemOpt.isPresent()) {
            System.out.println("Item not found.");
            return;
        }
        
        Item item = itemOpt.get();
        System.out.println("Item: " + item.getName());
        
        System.out.print("Enter batch ID: ");
        String batchId = scanner.nextLine().trim();
        
        System.out.print("Enter quantity: ");
        int quantity;
        try {
            quantity = Integer.parseInt(scanner.nextLine().trim());
            if (quantity <= 0) {
                System.out.println("Quantity must be positive. Batch not added.");
                return;
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid quantity. Batch not added.");
            return;
        }
        
        System.out.print("Enter received date (yyyy-MM-dd): ");
        String receivedDateStr = scanner.nextLine().trim();
        LocalDate receivedDate = DateUtil.parseDate(receivedDateStr);
        if (receivedDate == null) {
            System.out.println("Invalid date format. Batch not added.");
            return;
        }
        
        System.out.print("Enter expiry date (yyyy-MM-dd): ");
        String expiryDateStr = scanner.nextLine().trim();
        LocalDate expiryDate = DateUtil.parseDate(expiryDateStr);
        if (expiryDate == null) {
            System.out.println("Invalid date format. Batch not added.");
            return;
        }
        
        if (expiryDate.isBefore(receivedDate)) {
            System.out.println("Expiry date cannot be before received date. Batch not added.");
            return;
        }
        
        System.out.println("Select stock type:");
        System.out.println("1. Shelf");
        System.out.println("2. Storage");
        System.out.println("3. Online");
        System.out.print("Enter choice: ");
        
        int stockTypeChoice;
        try {
            stockTypeChoice = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("Invalid choice. Batch not added.");
            return;
        }
        
        StockType stockType;
        switch (stockTypeChoice) {
            case 1:
                stockType = StockType.SHELF;
                break;
            case 2:
                stockType = StockType.STORAGE;
                break;
            case 3:
                stockType = StockType.ONLINE;
                break;
            default:
                System.out.println("Invalid choice. Batch not added.");
                return;
        }
        
        StockBatch stockBatch = new StockBatch(batchId, item, quantity, receivedDate, expiryDate, stockType);
        inventoryService.addStockBatch(stockBatch);
        
        System.out.println("Stock batch added successfully.");
    }
    
    /**
     * Displays all items in the inventory.
     */
    private void viewAllItems() {
        System.out.println("\n===== All Items =====");
        
        List<Item> items = inventoryService.getAllItems();
        
        if (items.isEmpty()) {
            System.out.println("No items found.");
            return;
        }
        
        System.out.println(String.format("%-10s %-20s %-10s %-15s", "Code", "Name", "Price", "Category"));
        System.out.println("--------------------------------------------------");
        
        for (Item item : items) {
            System.out.println(String.format("%-10s %-20s $%-9.2f %-15s",
                    item.getCode(),
                    item.getName(),
                    item.getPrice(),
                    item.getCategory()));
        }
    }
    
    /**
     * Displays stock information for a specific item.
     */
    private void viewItemStock() {
        System.out.println("\n===== View Item Stock =====");
        
        System.out.print("Enter item code: ");
        String itemCode = scanner.nextLine().trim();
        
        Optional<Item> itemOpt = inventoryService.findItemByCode(itemCode);
        if (!itemOpt.isPresent()) {
            System.out.println("Item not found.");
            return;
        }
        
        Item item = itemOpt.get();
        System.out.println("Item: " + item.getName());
        
        List<StockBatch> batches = inventoryService.getStockBatchesByItemCode(itemCode);
        
        if (batches.isEmpty()) {
            System.out.println("No stock found for this item.");
            return;
        }
        
        System.out.println(String.format("%-10s %-10s %-12s %-12s %-10s",
                "Batch ID", "Quantity", "Received", "Expiry", "Type"));
        System.out.println("--------------------------------------------------");
        
        for (StockBatch batch : batches) {
            System.out.println(String.format("%-10s %-10d %-12s %-12s %-10s",
                    batch.getBatchId(),
                    batch.getQuantity(),
                    DateUtil.formatDate(batch.getReceivedDate()),
                    DateUtil.formatDate(batch.getExpiryDate()),
                    batch.getStockType()));
        }
        
        // Show totals
        int shelfTotal = inventoryService.getTotalQuantity(itemCode, StockType.SHELF);
        int storageTotal = inventoryService.getTotalQuantity(itemCode, StockType.STORAGE);
        int onlineTotal = inventoryService.getTotalQuantity(itemCode, StockType.ONLINE);
        
        System.out.println("\nTotal Quantities:");
        System.out.println("Shelf: " + shelfTotal);
        System.out.println("Storage: " + storageTotal);
        System.out.println("Online: " + onlineTotal);
        System.out.println("Overall Total: " + (shelfTotal + storageTotal + onlineTotal));
    }
    
    /**
     * Moves items from storage to shelf.
     */
    private void moveItemsToShelf() {
        System.out.println("\n===== Move Items to Shelf =====");
        
        System.out.print("Enter item code: ");
        String itemCode = scanner.nextLine().trim();
        
        Optional<Item> itemOpt = inventoryService.findItemByCode(itemCode);
        if (!itemOpt.isPresent()) {
            System.out.println("Item not found.");
            return;
        }
        
        Item item = itemOpt.get();
        System.out.println("Item: " + item.getName());
        
        int shelfQuantity = inventoryService.getTotalQuantity(itemCode, StockType.SHELF);
        int storageQuantity = inventoryService.getTotalQuantity(itemCode, StockType.STORAGE);
        
        System.out.println("Current shelf quantity: " + shelfQuantity);
        System.out.println("Current storage quantity: " + storageQuantity);
        
        if (storageQuantity == 0) {
            System.out.println("No items in storage to move.");
            return;
        }
        
        System.out.print("Enter quantity to move: ");
        int quantity;
        try {
            quantity = Integer.parseInt(scanner.nextLine().trim());
            if (quantity <= 0) {
                System.out.println("Quantity must be positive.");
                return;
            }
            if (quantity > storageQuantity) {
                System.out.println("Not enough items in storage.");
                return;
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid quantity.");
            return;
        }
        
        boolean success = inventoryService.moveFromStorageToShelf(itemCode, quantity);
        
        if (success) {
            System.out.println("Items moved successfully.");
            
            // Show updated quantities
            shelfQuantity = inventoryService.getTotalQuantity(itemCode, StockType.SHELF);
            storageQuantity = inventoryService.getTotalQuantity(itemCode, StockType.STORAGE);
            
            System.out.println("Updated shelf quantity: " + shelfQuantity);
            System.out.println("Updated storage quantity: " + storageQuantity);
        } else {
            System.out.println("Failed to move items.");
        }
    }
}
