package com.syos.test.service;

import com.syos.model.*;
import com.syos.repository.BillRepository;
import com.syos.repository.ItemRepository;
import com.syos.repository.TransactionRepository;
import com.syos.service.BillingService;
import com.syos.service.InventoryService;
import com.syos.test.base.BaseTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for the BillingService class.
 * Uses <PERSON>ckito to mock the repository and service dependencies.
 */
public class BillingServiceTest extends BaseTest {
    
    @Mock
    private ItemRepository itemRepository;
    
    @Mock
    private TransactionRepository transactionRepository;
    
    @Mock
    private BillRepository billRepository;
    
    @Mock
    private InventoryService inventoryService;
    
    private BillingService billingService;
    
    /**
     * Sets up the test environment.
     * Initializes mocks and creates a BillingService instance.
     */
    @Before
    public void setUpService() {
        MockitoAnnotations.initMocks(this);
        billingService = new BillingService(itemRepository, transactionRepository, billRepository, inventoryService);
    }
    
    /**
     * Tests creating a transaction.
     */
    @Test
    public void testCreateTransaction() {
        // Create a test user
        User user = new User("U001", "testuser", "password", "Test User", 
                "<EMAIL>", "123 Test St", "123-456-7890", true);
        
        // Mock the repository behavior
        when(transactionRepository.getNextTransactionId()).thenReturn("T001");
        
        // Call the service method for in-store transaction
        Transaction inStoreTransaction = billingService.createTransaction(null, TransactionType.IN_STORE);
        
        // Verify the in-store transaction
        assertNotNull(inStoreTransaction);
        assertEquals("T001", inStoreTransaction.getTransactionId());
        assertNull(inStoreTransaction.getUser());
        assertEquals(TransactionType.IN_STORE, inStoreTransaction.getType());
        
        // Call the service method for online transaction
        Transaction onlineTransaction = billingService.createTransaction(user, TransactionType.ONLINE);
        
        // Verify the online transaction
        assertNotNull(onlineTransaction);
        assertEquals("T001", onlineTransaction.getTransactionId());
        assertEquals(user, onlineTransaction.getUser());
        assertEquals(TransactionType.ONLINE, onlineTransaction.getType());
        
        // Verify the repository was called
        verify(transactionRepository, times(2)).getNextTransactionId();
    }
    
    /**
     * Tests adding an item to a transaction.
     */
    @Test
    public void testAddItemToTransaction() {
        // Create a test transaction and item
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        
        // Mock the repository behavior
        when(itemRepository.findByCode("I001")).thenReturn(Optional.of(item));
        when(itemRepository.findByCode("I999")).thenReturn(Optional.empty());
        when(inventoryService.hasSufficientStock("I001", 5, StockType.SHELF)).thenReturn(true);
        when(inventoryService.hasSufficientStock("I001", 200, StockType.SHELF)).thenReturn(false);
        
        // Call the service method
        boolean result1 = billingService.addItemToTransaction(transaction, "I001", 5);
        boolean result2 = billingService.addItemToTransaction(transaction, "I999", 5);
        boolean result3 = billingService.addItemToTransaction(transaction, "I001", 200);
        
        // Verify the results
        assertTrue(result1);
        assertFalse(result2); // Item not found
        assertFalse(result3); // Insufficient stock
        
        // Verify the transaction state
        assertEquals(1, transaction.getItems().size());
        assertEquals(item, transaction.getItems().get(0).getItem());
        assertEquals(5, transaction.getItems().get(0).getQuantity());
        
        // Verify the repository was called
        verify(itemRepository, times(2)).findByCode("I001");
        verify(itemRepository).findByCode("I999");
        verify(inventoryService, times(2)).hasSufficientStock("I001", anyInt(), eq(StockType.SHELF));
    }
    
    /**
     * Tests calculating the total amount for a transaction.
     */
    @Test
    public void testCalculateTotal() {
        // Create a test transaction and items
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        Item item1 = new Item("I001", "Item 1", 10.0, "Test");
        Item item2 = new Item("I002", "Item 2", 20.0, "Test");
        
        // Add items to the transaction
        transaction.addItem(item1, 2); // 2 * 10.0 = 20.0
        transaction.addItem(item2, 1); // 1 * 20.0 = 20.0
        
        // Call the service method
        double total = billingService.calculateTotal(transaction);
        
        // Verify the result
        assertEquals(40.0, total, 0.001);
        assertEquals(40.0, transaction.getTotalAmount(), 0.001);
    }
    
    /**
     * Tests applying a discount to a transaction.
     */
    @Test
    public void testApplyDiscount() {
        // Create a test transaction and item
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        
        // Add an item to the transaction
        transaction.addItem(item, 5); // 5 * 10.0 = 50.0
        
        // Calculate the total
        transaction.calculateTotal();
        
        // Call the service method
        billingService.applyDiscount(transaction, 10);
        
        // Verify the result
        assertEquals(5.0, transaction.getDiscountAmount(), 0.001); // 10% of 50.0
        assertEquals(45.0, transaction.getFinalAmount(), 0.001); // 50.0 - 5.0
    }
    
    /**
     * Tests processing payment for a transaction.
     */
    @Test
    public void testProcessPayment() {
        // Create a test transaction and item
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        Item item = new Item("I001", "Test Item", 10.0, "Test");
        
        // Add an item to the transaction
        transaction.addItem(item, 5); // 5 * 10.0 = 50.0
        
        // Calculate the total
        transaction.calculateTotal();
        
        // Call the service method
        double change1 = billingService.processPayment(transaction, 60.0);
        
        // Verify the result
        assertEquals(10.0, change1, 0.001);
        assertEquals(60.0, transaction.getCashTendered(), 0.001);
        assertEquals(10.0, transaction.getChange(), 0.001);
        
        // Test insufficient payment
        double change2 = billingService.processPayment(transaction, 40.0);
        
        // Verify the result
        assertEquals(-1.0, change2, 0.001);
    }
    
    /**
     * Tests completing a transaction.
     */
    @Test
    public void testCompleteTransaction() {
        // Create a test transaction and item
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);
        Item item1 = new Item("I001", "Item 1", 10.0, "Test");
        Item item2 = new Item("I002", "Item 2", 20.0, "Test");
        
        // Add items to the transaction
        transaction.addItem(item1, 2);
        transaction.addItem(item2, 1);
        
        // Calculate total and process payment
        transaction.calculateTotal();
        transaction.processPayment(50.0);
        
        // Mock the repository behavior
        when(billRepository.getNextBillNumber()).thenReturn("B001");
        when(transactionRepository.save(transaction)).thenReturn(transaction);
        when(billRepository.save(any(Bill.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // Call the service method
        Bill bill = billingService.completeTransaction(transaction);
        
        // Verify the result
        assertNotNull(bill);
        assertEquals("B001", bill.getBillNumber());
        assertEquals(transaction, bill.getTransaction());
        
        // Verify the repository was called
        verify(transactionRepository).save(transaction);
        verify(billRepository).getNextBillNumber();
        verify(billRepository).save(any(Bill.class));
        
        // Verify inventory was updated
        verify(inventoryService).reduceStock("I001", 2, StockType.SHELF);
        verify(inventoryService).reduceStock("I002", 1, StockType.SHELF);
    }
}
