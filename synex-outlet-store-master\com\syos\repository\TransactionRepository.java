package com.syos.repository;

import com.syos.model.Transaction;
import com.syos.model.TransactionType;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Transaction data operations.
 */
public interface TransactionRepository {
    
    /**
     * Saves a transaction to the repository.
     * 
     * @param transaction The transaction to save
     * @return The saved transaction
     */
    Transaction save(Transaction transaction);
    
    /**
     * Finds a transaction by its ID.
     * 
     * @param transactionId The transaction ID
     * @return An Optional containing the transaction if found, empty otherwise
     */
    Optional<Transaction> findById(String transactionId);
    
    /**
     * Finds all transactions for a specific user.
     * 
     * @param userId The user ID
     * @return A list of transactions for the user
     */
    List<Transaction> findByUserId(String userId);
    
    /**
     * Finds all transactions of a specific type.
     * 
     * @param type The transaction type
     * @return A list of transactions of the specified type
     */
    List<Transaction> findByType(TransactionType type);
    
    /**
     * Finds all transactions on a specific date.
     * 
     * @param date The date
     * @return A list of transactions on the specified date
     */
    List<Transaction> findByDate(LocalDate date);
    
    /**
     * Finds all transactions in the repository.
     * 
     * @return A list of all transactions
     */
    List<Transaction> findAll();
    
    /**
     * Deletes a transaction from the repository.
     * 
     * @param transactionId The ID of the transaction to delete
     * @return true if deleted, false otherwise
     */
    boolean delete(String transactionId);
    
    /**
     * Gets the next available transaction ID.
     * 
     * @return The next transaction ID
     */
    String getNextTransactionId();
}
