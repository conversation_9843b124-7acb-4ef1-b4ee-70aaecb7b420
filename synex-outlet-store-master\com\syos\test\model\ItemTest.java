package com.syos.test.model;

import com.syos.model.Item;
import com.syos.test.base.BaseTest;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * Unit tests for the Item model class.
 */
public class ItemTest extends BaseTest {
    
    /**
     * Tests the constructor and getters.
     */
    @Test
    public void testConstructorAndGetters() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");
        
        // Verify the values
        assertEquals("I001", item.getCode());
        assertEquals("Rice", item.getName());
        assertEquals(2.50, item.getPrice(), 0.001);
        assertEquals("Groceries", item.getCategory());
    }
    
    /**
     * Tests the setters.
     */
    @Test
    public void testSetters() {
        // Create an item
        Item item = new Item();
        
        // Set values
        item.setCode("I002");
        item.setName("Flour");
        item.setPrice(1.80);
        item.setCategory("Baking");
        
        // Verify the values
        assertEquals("I002", item.getCode());
        assertEquals("Flour", item.getName());
        assertEquals(1.80, item.getPrice(), 0.001);
        assertEquals("Baking", item.getCategory());
    }
    
    /**
     * Tests the equals method.
     */
    @Test
    public void testEquals() {
        // Create two items with the same code
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item2 = new Item("I001", "Rice (different brand)", 3.00, "Groceries");
        
        // Create an item with a different code
        Item item3 = new Item("I002", "Rice", 2.50, "Groceries");
        
        // Verify equality
        assertEquals(item1, item2); // Same code, should be equal
        assertNotEquals(item1, item3); // Different code, should not be equal
        assertNotEquals(item1, null); // Null comparison
        assertNotEquals(item1, "Not an item"); // Different type
    }
    
    /**
     * Tests the hashCode method.
     */
    @Test
    public void testHashCode() {
        // Create two items with the same code
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item2 = new Item("I001", "Rice (different brand)", 3.00, "Groceries");
        
        // Verify hash codes
        assertEquals(item1.hashCode(), item2.hashCode());
    }
    
    /**
     * Tests the toString method.
     */
    @Test
    public void testToString() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");
        
        // Verify the string representation
        String expected = "Item{code='I001', name='Rice', price=2.5, category='Groceries'}";
        assertEquals(expected, item.toString());
    }
}
