package com.syos.ui;

import com.syos.model.User;
import com.syos.service.UserService;

import java.util.List;
import java.util.Optional;
import java.util.Scanner;

/**
 * Menu for user management operations.
 */
public class UserMenu {
    
    private final Scanner scanner;
    private final UserService userService;
    private User currentUser;
    
    public UserMenu(Scanner scanner, UserService userService) {
        this.scanner = scanner;
        this.userService = userService;
    }
    
    /**
     * Displays the user menu and handles user input.
     */
    public void displayMenu() {
        boolean back = false;
        
        while (!back) {
            System.out.println("\n===== User Management =====");
            if (currentUser == null) {
                System.out.println("1. Register New User");
                System.out.println("2. Login");
                System.out.println("3. View All Users");
                System.out.println("4. Back to Main Menu");
            } else {
                System.out.println("Logged in as: " + currentUser.getUsername() + " (" + currentUser.getFullName() + ")");
                System.out.println("1. Register New User");
                System.out.println("2. Logout");
                System.out.println("3. View All Users");
                System.out.println("4. Update Profile");
                System.out.println("5. Back to Main Menu");
            }
            
            System.out.print("Enter your choice: ");
            
            int choice;
            try {
                choice = Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Invalid input. Please enter a number.");
                continue;
            }
            
            if (currentUser == null) {
                switch (choice) {
                    case 1:
                        registerUser();
                        break;
                    case 2:
                        login();
                        break;
                    case 3:
                        viewAllUsers();
                        break;
                    case 4:
                        back = true;
                        break;
                    default:
                        System.out.println("Invalid choice. Please try again.");
                }
            } else {
                switch (choice) {
                    case 1:
                        registerUser();
                        break;
                    case 2:
                        logout();
                        break;
                    case 3:
                        viewAllUsers();
                        break;
                    case 4:
                        updateProfile();
                        break;
                    case 5:
                        back = true;
                        break;
                    default:
                        System.out.println("Invalid choice. Please try again.");
                }
            }
        }
    }
    
    /**
     * Registers a new user.
     */
    private void registerUser() {
        System.out.println("\n===== Register New User =====");
        
        System.out.print("Enter user ID: ");
        String userId = scanner.nextLine().trim();
        
        // Check if user ID already exists
        if (userService.getUserById(userId).isPresent()) {
            System.out.println("User with ID " + userId + " already exists.");
            return;
        }
        
        System.out.print("Enter username: ");
        String username = scanner.nextLine().trim();
        
        // Check if username already exists
        if (userService.getUserByUsername(username).isPresent()) {
            System.out.println("Username " + username + " is already taken.");
            return;
        }
        
        System.out.print("Enter password: ");
        String password = scanner.nextLine().trim();
        
        System.out.print("Enter full name: ");
        String fullName = scanner.nextLine().trim();
        
        System.out.print("Enter email: ");
        String email = scanner.nextLine().trim();
        
        System.out.print("Enter address: ");
        String address = scanner.nextLine().trim();
        
        System.out.print("Enter phone: ");
        String phone = scanner.nextLine().trim();
        
        System.out.print("Is this an online customer? (y/n): ");
        boolean isOnlineCustomer = scanner.nextLine().trim().toLowerCase().startsWith("y");
        
        User user = new User(userId, username, password, fullName, email, address, phone, isOnlineCustomer);
        
        try {
            userService.registerUser(user);
            System.out.println("User registered successfully.");
        } catch (IllegalArgumentException e) {
            System.out.println("Registration failed: " + e.getMessage());
        }
    }
    
    /**
     * Logs in a user.
     */
    private void login() {
        System.out.println("\n===== Login =====");
        
        System.out.print("Enter username: ");
        String username = scanner.nextLine().trim();
        
        System.out.print("Enter password: ");
        String password = scanner.nextLine().trim();
        
        Optional<User> userOpt = userService.authenticateUser(username, password);
        
        if (userOpt.isPresent()) {
            currentUser = userOpt.get();
            System.out.println("Login successful. Welcome, " + currentUser.getFullName() + "!");
        } else {
            System.out.println("Login failed. Invalid username or password.");
        }
    }
    
    /**
     * Logs out the current user.
     */
    private void logout() {
        currentUser = null;
        System.out.println("Logged out successfully.");
    }
    
    /**
     * Displays all users.
     */
    private void viewAllUsers() {
        System.out.println("\n===== All Users =====");
        
        List<User> users = userService.getAllUsers();
        
        if (users.isEmpty()) {
            System.out.println("No users found.");
            return;
        }
        
        System.out.println(String.format("%-10s %-15s %-20s %-25s %-15s",
                "User ID", "Username", "Full Name", "Email", "Online Customer"));
        System.out.println("--------------------------------------------------------------------------------");
        
        for (User user : users) {
            System.out.println(String.format("%-10s %-15s %-20s %-25s %-15s",
                    user.getUserId(),
                    user.getUsername(),
                    user.getFullName(),
                    user.getEmail(),
                    user.isOnlineCustomer() ? "Yes" : "No"));
        }
    }
    
    /**
     * Updates the current user's profile.
     */
    private void updateProfile() {
        if (currentUser == null) {
            System.out.println("You must be logged in to update your profile.");
            return;
        }
        
        System.out.println("\n===== Update Profile =====");
        
        System.out.println("Current full name: " + currentUser.getFullName());
        System.out.print("Enter new full name (or leave blank to keep current): ");
        String fullName = scanner.nextLine().trim();
        if (!fullName.isEmpty()) {
            currentUser.setFullName(fullName);
        }
        
        System.out.println("Current email: " + currentUser.getEmail());
        System.out.print("Enter new email (or leave blank to keep current): ");
        String email = scanner.nextLine().trim();
        if (!email.isEmpty()) {
            currentUser.setEmail(email);
        }
        
        System.out.println("Current address: " + currentUser.getAddress());
        System.out.print("Enter new address (or leave blank to keep current): ");
        String address = scanner.nextLine().trim();
        if (!address.isEmpty()) {
            currentUser.setAddress(address);
        }
        
        System.out.println("Current phone: " + currentUser.getPhone());
        System.out.print("Enter new phone (or leave blank to keep current): ");
        String phone = scanner.nextLine().trim();
        if (!phone.isEmpty()) {
            currentUser.setPhone(phone);
        }
        
        System.out.print("Change password? (y/n): ");
        boolean changePassword = scanner.nextLine().trim().toLowerCase().startsWith("y");
        
        if (changePassword) {
            System.out.print("Enter current password: ");
            String currentPassword = scanner.nextLine().trim();
            
            if (!currentPassword.equals(currentUser.getPassword())) {
                System.out.println("Incorrect password. Profile not updated.");
                return;
            }
            
            System.out.print("Enter new password: ");
            String newPassword = scanner.nextLine().trim();
            
            if (!newPassword.isEmpty()) {
                currentUser.setPassword(newPassword);
            }
        }
        
        userService.updateUser(currentUser);
        System.out.println("Profile updated successfully.");
    }
    
    /**
     * Gets the currently logged-in user.
     * 
     * @return The current user, or null if no user is logged in
     */
    public User getCurrentUser() {
        return currentUser;
    }
}
