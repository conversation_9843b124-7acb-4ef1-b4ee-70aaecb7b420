package com.syos.repository;

import com.syos.model.User;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for User data operations.
 */
public interface UserRepository {
    
    /**
     * Saves a user to the repository.
     * 
     * @param user The user to save
     * @return The saved user
     */
    User save(User user);
    
    /**
     * Finds a user by their ID.
     * 
     * @param userId The user ID
     * @return An Optional containing the user if found, empty otherwise
     */
    Optional<User> findById(String userId);
    
    /**
     * Finds a user by their username.
     * 
     * @param username The username
     * @return An Optional containing the user if found, empty otherwise
     */
    Optional<User> findByUsername(String username);
    
    /**
     * Finds all users in the repository.
     * 
     * @return A list of all users
     */
    List<User> findAll();
    
    /**
     * Updates an existing user in the repository.
     * 
     * @param user The user to update
     * @return The updated user
     */
    User update(User user);
    
    /**
     * Deletes a user from the repository.
     * 
     * @param userId The ID of the user to delete
     * @return true if deleted, false otherwise
     */
    boolean delete(String userId);
    
    /**
     * Authenticates a user with username and password.
     * 
     * @param username The username
     * @param password The password
     * @return An Optional containing the authenticated user if successful, empty otherwise
     */
    Optional<User> authenticate(String username, String password);
}
