package com.syos.test;

import com.syos.model.*;
import com.syos.repository.*;
import com.syos.repository.impl.*;
import com.syos.service.BillingService;
import com.syos.service.InventoryService;
import com.syos.util.FileUtil;

import java.time.LocalDate;
import java.util.Optional;

/**
 * Test class for the BillingService.
 */
public class BillingServiceTest {
    
    private static final String TEST_DATA_DIRECTORY = "test_data";
    
    public static void main(String[] args) {
        System.out.println("Running BillingService tests...");
        
        // Ensure test data directory exists
        FileUtil.ensureDirectoryExists(TEST_DATA_DIRECTORY);
        
        // Initialize repositories and services
        ItemRepository itemRepository = new FileItemRepository();
        StockRepository stockRepository = new FileStockRepository();
        TransactionRepository transactionRepository = new FileTransactionRepository();
        BillRepository billRepository = new FileBillRepository();
        InventoryService inventoryService = new InventoryService(itemRepository, stockRepository);
        BillingService billingService = new BillingService(
                itemRepository, transactionRepository, billRepository, inventoryService);
        
        // Setup test data
        setupTestData(itemRepository, stockRepository);
        
        // Run tests
        testCreateTransaction(billingService);
        testAddItemToTransaction(billingService, inventoryService);
        testCalculateTotal(billingService);
        testApplyDiscount(billingService);
        testProcessPayment(billingService);
        testCompleteTransaction(billingService, inventoryService);
        
        System.out.println("All tests completed.");
    }
    
    /**
     * Sets up test data.
     * 
     * @param itemRepository The item repository
     * @param stockRepository The stock repository
     */
    private static void setupTestData(ItemRepository itemRepository, StockRepository stockRepository) {
        // Create a test item
        Item item = new Item("B001", "Test Billing Item", 10.00, "Test");
        itemRepository.save(item);
        
        // Create a test stock batch
        LocalDate today = LocalDate.now();
        StockBatch stockBatch = new StockBatch(
                "BB001",
                item,
                100,
                today,
                today.plusMonths(6),
                StockType.SHELF
        );
        stockRepository.save(stockBatch);
    }
    
    /**
     * Tests creating a transaction.
     * 
     * @param billingService The billing service
     */
    private static void testCreateTransaction(BillingService billingService) {
        System.out.println("\nTesting createTransaction...");
        
        // Create a transaction
        Transaction transaction = billingService.createTransaction(null, TransactionType.IN_STORE);
        
        if (transaction != null && transaction.getTransactionId() != null && 
                transaction.getType() == TransactionType.IN_STORE) {
            System.out.println("createTransaction test passed.");
        } else {
            System.out.println("createTransaction test failed.");
        }
    }
    
    /**
     * Tests adding an item to a transaction.
     * 
     * @param billingService The billing service
     * @param inventoryService The inventory service
     */
    private static void testAddItemToTransaction(BillingService billingService, InventoryService inventoryService) {
        System.out.println("\nTesting addItemToTransaction...");
        
        // Create a transaction
        Transaction transaction = billingService.createTransaction(null, TransactionType.IN_STORE);
        
        // Add an item to the transaction
        boolean result = billingService.addItemToTransaction(transaction, "B001", 5);
        
        if (result && transaction.getItems().size() == 1 && 
                transaction.getItems().get(0).getQuantity() == 5) {
            System.out.println("addItemToTransaction test passed.");
        } else {
            System.out.println("addItemToTransaction test failed.");
        }
    }
    
    /**
     * Tests calculating the total amount for a transaction.
     * 
     * @param billingService The billing service
     */
    private static void testCalculateTotal(BillingService billingService) {
        System.out.println("\nTesting calculateTotal...");
        
        // Create a transaction
        Transaction transaction = billingService.createTransaction(null, TransactionType.IN_STORE);
        
        // Add an item to the transaction
        billingService.addItemToTransaction(transaction, "B001", 5);
        
        // Calculate the total
        double total = billingService.calculateTotal(transaction);
        
        if (total == 50.0) { // 5 items at $10.00 each
            System.out.println("calculateTotal test passed.");
        } else {
            System.out.println("calculateTotal test failed. Expected: 50.0, Actual: " + total);
        }
    }
    
    /**
     * Tests applying a discount to a transaction.
     * 
     * @param billingService The billing service
     */
    private static void testApplyDiscount(BillingService billingService) {
        System.out.println("\nTesting applyDiscount...");
        
        // Create a transaction
        Transaction transaction = billingService.createTransaction(null, TransactionType.IN_STORE);
        
        // Add an item to the transaction
        billingService.addItemToTransaction(transaction, "B001", 5);
        
        // Calculate the total
        billingService.calculateTotal(transaction);
        
        // Apply a 10% discount
        billingService.applyDiscount(transaction, 10);
        
        if (transaction.getDiscountAmount() == 5.0 && transaction.getFinalAmount() == 45.0) {
            System.out.println("applyDiscount test passed.");
        } else {
            System.out.println("applyDiscount test failed. Expected discount: 5.0, Actual: " + 
                    transaction.getDiscountAmount() + ", Expected final: 45.0, Actual: " + 
                    transaction.getFinalAmount());
        }
    }
    
    /**
     * Tests processing payment for a transaction.
     * 
     * @param billingService The billing service
     */
    private static void testProcessPayment(BillingService billingService) {
        System.out.println("\nTesting processPayment...");
        
        // Create a transaction
        Transaction transaction = billingService.createTransaction(null, TransactionType.IN_STORE);
        
        // Add an item to the transaction
        billingService.addItemToTransaction(transaction, "B001", 5);
        
        // Calculate the total
        billingService.calculateTotal(transaction);
        
        // Apply a 10% discount
        billingService.applyDiscount(transaction, 10);
        
        // Process payment
        double change = billingService.processPayment(transaction, 50.0);
        
        if (change == 5.0 && transaction.getCashTendered() == 50.0 && transaction.getChange() == 5.0) {
            System.out.println("processPayment test passed.");
        } else {
            System.out.println("processPayment test failed. Expected change: 5.0, Actual: " + change);
        }
    }
    
    /**
     * Tests completing a transaction.
     * 
     * @param billingService The billing service
     * @param inventoryService The inventory service
     */
    private static void testCompleteTransaction(BillingService billingService, InventoryService inventoryService) {
        System.out.println("\nTesting completeTransaction...");
        
        // Get initial stock quantity
        int initialQuantity = inventoryService.getTotalQuantity("B001", StockType.SHELF);
        
        // Create a transaction
        Transaction transaction = billingService.createTransaction(null, TransactionType.IN_STORE);
        
        // Add an item to the transaction
        billingService.addItemToTransaction(transaction, "B001", 5);
        
        // Calculate the total
        billingService.calculateTotal(transaction);
        
        // Apply a 10% discount
        billingService.applyDiscount(transaction, 10);
        
        // Process payment
        billingService.processPayment(transaction, 50.0);
        
        // Complete the transaction
        Bill bill = billingService.completeTransaction(transaction);
        
        // Get updated stock quantity
        int updatedQuantity = inventoryService.getTotalQuantity("B001", StockType.SHELF);
        
        if (bill != null && bill.getBillNumber() != null && 
                updatedQuantity == initialQuantity - 5) {
            System.out.println("completeTransaction test passed.");
        } else {
            System.out.println("completeTransaction test failed.");
        }
    }
}
