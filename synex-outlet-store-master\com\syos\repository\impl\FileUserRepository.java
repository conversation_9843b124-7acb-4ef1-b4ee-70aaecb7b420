package com.syos.repository.impl;

import com.syos.model.User;
import com.syos.repository.UserRepository;
import com.syos.util.FileUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * File-based implementation of the UserRepository interface.
 */
public class FileUserRepository implements UserRepository {
    
    private static final String DATA_DIRECTORY = "data";
    private static final String USERS_FILE = DATA_DIRECTORY + "/users.csv";
    private static final String DELIMITER = ",";
    
    /**
     * Converts a User object to a CSV line.
     * 
     * @param user The user to convert
     * @return The CSV line
     */
    private String userToCsvLine(User user) {
        return String.join(DELIMITER,
                user.getUserId(),
                user.getUsername(),
                user.getPassword(),
                user.getFullName(),
                user.getEmail(),
                user.getAddress().replace(",", ";"), // Escape commas in address
                user.getPhone(),
                String.valueOf(user.isOnlineCustomer()));
    }
    
    /**
     * Converts a CSV line to a User object.
     * 
     * @param line The CSV line
     * @return The User object, or null if conversion fails
     */
    private User csvLineToUser(String line) {
        String[] parts = line.split(DELIMITER);
        if (parts.length >= 8) {
            String userId = parts[0];
            String username = parts[1];
            String password = parts[2];
            String fullName = parts[3];
            String email = parts[4];
            String address = parts[5].replace(";", ","); // Restore commas in address
            String phone = parts[6];
            boolean isOnlineCustomer = Boolean.parseBoolean(parts[7]);
            
            return new User(userId, username, password, fullName, email, address, phone, isOnlineCustomer);
        }
        return null;
    }
    
    @Override
    public User save(User user) {
        List<User> users = findAll();
        
        // Check if user with the same ID already exists
        Optional<User> existingUser = users.stream()
                .filter(u -> u.getUserId().equals(user.getUserId()))
                .findFirst();
        
        if (existingUser.isPresent()) {
            // Update existing user
            return update(user);
        } else {
            // Add new user
            List<String> lines = users.stream()
                    .map(this::userToCsvLine)
                    .collect(Collectors.toList());
            
            lines.add(userToCsvLine(user));
            
            FileUtil.writeLinesToFile(USERS_FILE, lines);
            return user;
        }
    }
    
    @Override
    public Optional<User> findById(String userId) {
        return findAll().stream()
                .filter(user -> user.getUserId().equals(userId))
                .findFirst();
    }
    
    @Override
    public Optional<User> findByUsername(String username) {
        return findAll().stream()
                .filter(user -> user.getUsername().equals(username))
                .findFirst();
    }
    
    @Override
    public List<User> findAll() {
        List<String> lines = FileUtil.readLinesFromFile(USERS_FILE);
        List<User> users = new ArrayList<>();
        
        for (String line : lines) {
            User user = csvLineToUser(line);
            if (user != null) {
                users.add(user);
            }
        }
        
        return users;
    }
    
    @Override
    public User update(User user) {
        List<User> users = findAll();
        List<User> updatedUsers = users.stream()
                .map(u -> u.getUserId().equals(user.getUserId()) ? user : u)
                .collect(Collectors.toList());
        
        List<String> lines = updatedUsers.stream()
                .map(this::userToCsvLine)
                .collect(Collectors.toList());
        
        FileUtil.writeLinesToFile(USERS_FILE, lines);
        return user;
    }
    
    @Override
    public boolean delete(String userId) {
        List<User> users = findAll();
        List<User> updatedUsers = users.stream()
                .filter(user -> !user.getUserId().equals(userId))
                .collect(Collectors.toList());
        
        if (updatedUsers.size() < users.size()) {
            List<String> lines = updatedUsers.stream()
                    .map(this::userToCsvLine)
                    .collect(Collectors.toList());
            
            return FileUtil.writeLinesToFile(USERS_FILE, lines);
        }
        
        return false;
    }
    
    @Override
    public Optional<User> authenticate(String username, String password) {
        return findAll().stream()
                .filter(user -> user.getUsername().equals(username) && user.getPassword().equals(password))
                .findFirst();
    }
}
