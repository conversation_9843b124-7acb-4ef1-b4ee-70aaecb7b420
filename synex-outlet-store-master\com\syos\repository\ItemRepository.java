package com.syos.repository;

import com.syos.model.Item;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Item data operations.
 */
public interface ItemRepository {
    
    /**
     * Saves an item to the repository.
     * 
     * @param item The item to save
     * @return The saved item
     */
    Item save(Item item);
    
    /**
     * Finds an item by its code.
     * 
     * @param code The item code
     * @return An Optional containing the item if found, empty otherwise
     */
    Optional<Item> findByCode(String code);
    
    /**
     * Finds all items in the repository.
     * 
     * @return A list of all items
     */
    List<Item> findAll();
    
    /**
     * Deletes an item from the repository.
     * 
     * @param code The code of the item to delete
     * @return true if deleted, false otherwise
     */
    boolean delete(String code);
    
    /**
     * Updates an existing item in the repository.
     * 
     * @param item The item to update
     * @return The updated item
     */
    Item update(Item item);
}
