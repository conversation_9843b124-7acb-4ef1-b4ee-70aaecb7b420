package com.syos.model;

/**
 * Represents a user in the system.
 * Users are required for online purchases.
 */
public class User {
    private String userId;
    private String username;
    private String password; // In a real system, this would be hashed
    private String fullName;
    private String email;
    private String address;
    private String phone;
    private boolean isOnlineCustomer;
    
    public User() {
    }
    
    public User(String userId, String username, String password, String fullName, 
                String email, String address, String phone, boolean isOnlineCustomer) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.fullName = fullName;
        this.email = email;
        this.address = address;
        this.phone = phone;
        this.isOnlineCustomer = isOnlineCustomer;
    }
    
    // Getters and Setters
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public boolean isOnlineCustomer() {
        return isOnlineCustomer;
    }
    
    public void setOnlineCustomer(boolean onlineCustomer) {
        isOnlineCustomer = onlineCustomer;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", address='" + address + '\'' +
                ", phone='" + phone + '\'' +
                ", isOnlineCustomer=" + isOnlineCustomer +
                '}';
    }
}
