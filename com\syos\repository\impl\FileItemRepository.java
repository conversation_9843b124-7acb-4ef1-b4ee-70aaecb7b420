package com.syos.repository.impl;

import com.syos.model.Item;
import com.syos.repository.ItemRepository;
import com.syos.util.FileUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * File-based implementation of the ItemRepository interface.
 */
public class FileItemRepository implements ItemRepository {
    
    private static final String DATA_DIRECTORY = "data";
    private static final String ITEMS_FILE = DATA_DIRECTORY + "/items.csv";
    private static final String DELIMITER = ",";
    
    /**
     * Converts an Item object to a CSV line.
     * 
     * @param item The item to convert
     * @return The CSV line
     */
    private String itemToCsvLine(Item item) {
        return String.join(DELIMITER,
                item.getCode(),
                item.getName(),
                String.valueOf(item.getPrice()),
                item.getCategory());
    }
    
    /**
     * Converts a CSV line to an Item object.
     * 
     * @param line The CSV line
     * @return The Item object
     */
    private Item csvLineToItem(String line) {
        String[] parts = line.split(DELIMITER);
        if (parts.length >= 4) {
            Item item = new Item();
            item.setCode(parts[0]);
            item.setName(parts[1]);
            item.setPrice(Double.parseDouble(parts[2]));
            item.setCategory(parts[3]);
            return item;
        }
        return null;
    }
    
    @Override
    public Item save(Item item) {
        List<Item> items = findAll();
        
        // Check if item with the same code already exists
        Optional<Item> existingItem = items.stream()
                .filter(i -> i.getCode().equals(item.getCode()))
                .findFirst();
        
        if (existingItem.isPresent()) {
            // Update existing item
            return update(item);
        } else {
            // Add new item
            List<String> lines = items.stream()
                    .map(this::itemToCsvLine)
                    .collect(Collectors.toList());
            
            lines.add(itemToCsvLine(item));
            
            FileUtil.writeLinesToFile(ITEMS_FILE, lines);
            return item;
        }
    }
    
    @Override
    public Optional<Item> findByCode(String code) {
        return findAll().stream()
                .filter(item -> item.getCode().equals(code))
                .findFirst();
    }
    
    @Override
    public List<Item> findAll() {
        List<String> lines = FileUtil.readLinesFromFile(ITEMS_FILE);
        List<Item> items = new ArrayList<>();
        
        for (String line : lines) {
            Item item = csvLineToItem(line);
            if (item != null) {
                items.add(item);
            }
        }
        
        return items;
    }
    
    @Override
    public boolean delete(String code) {
        List<Item> items = findAll();
        List<Item> updatedItems = items.stream()
                .filter(item -> !item.getCode().equals(code))
                .collect(Collectors.toList());
        
        if (updatedItems.size() < items.size()) {
            List<String> lines = updatedItems.stream()
                    .map(this::itemToCsvLine)
                    .collect(Collectors.toList());
            
            return FileUtil.writeLinesToFile(ITEMS_FILE, lines);
        }
        
        return false;
    }
    
    @Override
    public Item update(Item item) {
        List<Item> items = findAll();
        List<Item> updatedItems = items.stream()
                .map(i -> i.getCode().equals(item.getCode()) ? item : i)
                .collect(Collectors.toList());
        
        List<String> lines = updatedItems.stream()
                .map(this::itemToCsvLine)
                .collect(Collectors.toList());
        
        FileUtil.writeLinesToFile(ITEMS_FILE, lines);
        return item;
    }
}
