package com.syos.model;

/**
 * Represents an item in a shopping cart.
 * Contains the item and the quantity selected.
 */
public class CartItem {
    private Item item;
    private int quantity;
    
    public CartItem() {
    }
    
    public CartItem(Item item, int quantity) {
        this.item = item;
        this.quantity = quantity;
    }
    
    // Getters and Setters
    public Item getItem() {
        return item;
    }
    
    public void setItem(Item item) {
        this.item = item;
    }
    
    public int getQuantity() {
        return quantity;
    }
    
    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }
    
    /**
     * Calculates the subtotal for this cart item.
     * 
     * @return The subtotal (price * quantity)
     */
    public double getSubtotal() {
        return item.getPrice() * quantity;
    }
    
    @Override
    public String toString() {
        return "CartItem{" +
                "item=" + item +
                ", quantity=" + quantity +
                ", subtotal=" + getSubtotal() +
                '}';
    }
}
