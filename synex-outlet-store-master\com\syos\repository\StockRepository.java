package com.syos.repository;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Stock data operations.
 */
public interface StockRepository {
    
    /**
     * Saves a stock batch to the repository.
     * 
     * @param stockBatch The stock batch to save
     * @return The saved stock batch
     */
    StockBatch save(StockBatch stockBatch);
    
    /**
     * Finds a stock batch by its ID.
     * 
     * @param batchId The batch ID
     * @return An Optional containing the stock batch if found, empty otherwise
     */
    Optional<StockBatch> findByBatchId(String batchId);
    
    /**
     * Finds all stock batches for a specific item.
     * 
     * @param itemCode The item code
     * @return A list of stock batches for the item
     */
    List<StockBatch> findByItemCode(String itemCode);
    
    /**
     * Finds all stock batches of a specific type.
     * 
     * @param stockType The stock type
     * @return A list of stock batches of the specified type
     */
    List<StockBatch> findByStockType(StockType stockType);
    
    /**
     * Finds all stock batches in the repository.
     * 
     * @return A list of all stock batches
     */
    List<StockBatch> findAll();
    
    /**
     * Updates an existing stock batch in the repository.
     * 
     * @param stockBatch The stock batch to update
     * @return The updated stock batch
     */
    StockBatch update(StockBatch stockBatch);
    
    /**
     * Deletes a stock batch from the repository.
     * 
     * @param batchId The ID of the batch to delete
     * @return true if deleted, false otherwise
     */
    boolean delete(String batchId);
    
    /**
     * Gets the total quantity of an item across all batches of a specific type.
     * 
     * @param itemCode The item code
     * @param stockType The stock type
     * @return The total quantity
     */
    int getTotalQuantity(String itemCode, StockType stockType);
}
