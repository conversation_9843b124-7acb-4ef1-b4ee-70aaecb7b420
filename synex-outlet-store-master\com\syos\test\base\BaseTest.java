package com.syos.test.base;

import com.syos.test.util.TestDataUtil;
import org.junit.After;
import org.junit.Before;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

/**
 * Base class for all tests.
 * Provides common setup and teardown methods.
 */
public abstract class BaseTest {
    
    protected final ByteArrayOutputStream outContent = new ByteArrayOutputStream();
    protected final ByteArrayOutputStream errContent = new ByteArrayOutputStream();
    protected final PrintStream originalOut = System.out;
    protected final PrintStream originalErr = System.err;
    
    /**
     * Sets up the test environment.
     * Creates the test data directory and redirects System.out and System.err.
     */
    @Before
    public void setUp() {
        TestDataUtil.setupTestDataDirectory();
        System.setOut(new PrintStream(outContent));
        System.setErr(new PrintStream(errContent));
    }
    
    /**
     * Tears down the test environment.
     * Cleans up the test data directory and restores System.out and System.err.
     */
    @After
    public void tearDown() {
        TestDataUtil.cleanupTestDataDirectory();
        System.setOut(originalOut);
        System.setErr(originalErr);
    }
}
