package com.syos.repository.impl;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.repository.ItemRepository;
import com.syos.repository.StockRepository;
import com.syos.util.DateUtil;
import com.syos.util.FileUtil;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * File-based implementation of the StockRepository interface.
 */
public class FileStockRepository implements StockRepository {
    
    private static final String DATA_DIRECTORY = "data";
    private static final String STOCK_FILE = DATA_DIRECTORY + "/stock.csv";
    private static final String DELIMITER = ",";
    
    private final ItemRepository itemRepository = new FileItemRepository();
    
    /**
     * Converts a StockBatch object to a CSV line.
     * 
     * @param stockBatch The stock batch to convert
     * @return The CSV line
     */
    private String stockBatchToCsvLine(StockBatch stockBatch) {
        return String.join(DELIMITER,
                stockBatch.getBatchId(),
                stockBatch.getItem().getCode(),
                String.valueOf(stockBatch.getQuantity()),
                DateUtil.formatDate(stockBatch.getReceivedDate()),
                DateUtil.formatDate(stockBatch.getExpiryDate()),
                stockBatch.getStockType().toString());
    }
    
    /**
     * Converts a CSV line to a StockBatch object.
     * 
     * @param line The CSV line
     * @return The StockBatch object, or null if conversion fails
     */
    private StockBatch csvLineToStockBatch(String line) {
        String[] parts = line.split(DELIMITER);
        if (parts.length >= 6) {
            String batchId = parts[0];
            String itemCode = parts[1];
            int quantity = Integer.parseInt(parts[2]);
            LocalDate receivedDate = DateUtil.parseDate(parts[3]);
            LocalDate expiryDate = DateUtil.parseDate(parts[4]);
            StockType stockType = StockType.valueOf(parts[5]);
            
            Optional<Item> itemOpt = itemRepository.findByCode(itemCode);
            if (itemOpt.isPresent()) {
                return new StockBatch(batchId, itemOpt.get(), quantity, receivedDate, expiryDate, stockType);
            }
        }
        return null;
    }
    
    @Override
    public StockBatch save(StockBatch stockBatch) {
        List<StockBatch> stockBatches = findAll();
        
        // Check if batch with the same ID already exists
        Optional<StockBatch> existingBatch = stockBatches.stream()
                .filter(b -> b.getBatchId().equals(stockBatch.getBatchId()))
                .findFirst();
        
        if (existingBatch.isPresent()) {
            // Update existing batch
            return update(stockBatch);
        } else {
            // Add new batch
            List<String> lines = stockBatches.stream()
                    .map(this::stockBatchToCsvLine)
                    .collect(Collectors.toList());
            
            lines.add(stockBatchToCsvLine(stockBatch));
            
            FileUtil.writeLinesToFile(STOCK_FILE, lines);
            return stockBatch;
        }
    }
    
    @Override
    public Optional<StockBatch> findByBatchId(String batchId) {
        return findAll().stream()
                .filter(batch -> batch.getBatchId().equals(batchId))
                .findFirst();
    }
    
    @Override
    public List<StockBatch> findByItemCode(String itemCode) {
        return findAll().stream()
                .filter(batch -> batch.getItem().getCode().equals(itemCode))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<StockBatch> findByStockType(StockType stockType) {
        return findAll().stream()
                .filter(batch -> batch.getStockType() == stockType)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<StockBatch> findAll() {
        List<String> lines = FileUtil.readLinesFromFile(STOCK_FILE);
        List<StockBatch> stockBatches = new ArrayList<>();
        
        for (String line : lines) {
            StockBatch stockBatch = csvLineToStockBatch(line);
            if (stockBatch != null) {
                stockBatches.add(stockBatch);
            }
        }
        
        return stockBatches;
    }
    
    @Override
    public StockBatch update(StockBatch stockBatch) {
        List<StockBatch> stockBatches = findAll();
        List<StockBatch> updatedBatches = stockBatches.stream()
                .map(b -> b.getBatchId().equals(stockBatch.getBatchId()) ? stockBatch : b)
                .collect(Collectors.toList());
        
        List<String> lines = updatedBatches.stream()
                .map(this::stockBatchToCsvLine)
                .collect(Collectors.toList());
        
        FileUtil.writeLinesToFile(STOCK_FILE, lines);
        return stockBatch;
    }
    
    @Override
    public boolean delete(String batchId) {
        List<StockBatch> stockBatches = findAll();
        List<StockBatch> updatedBatches = stockBatches.stream()
                .filter(batch -> !batch.getBatchId().equals(batchId))
                .collect(Collectors.toList());
        
        if (updatedBatches.size() < stockBatches.size()) {
            List<String> lines = updatedBatches.stream()
                    .map(this::stockBatchToCsvLine)
                    .collect(Collectors.toList());
            
            return FileUtil.writeLinesToFile(STOCK_FILE, lines);
        }
        
        return false;
    }
    
    @Override
    public int getTotalQuantity(String itemCode, StockType stockType) {
        return findByItemCode(itemCode).stream()
                .filter(batch -> batch.getStockType() == stockType)
                .mapToInt(StockBatch::getQuantity)
                .sum();
    }
}
