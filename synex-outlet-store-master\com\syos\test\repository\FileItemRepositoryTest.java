package com.syos.test.repository;

import com.syos.model.Item;
import com.syos.repository.ItemRepository;
import com.syos.repository.impl.FileItemRepository;
import com.syos.test.base.BaseTest;
import com.syos.test.util.TestDataUtil;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;

/**
 * Unit tests for the FileItemRepository class.
 * Tests the CRUD operations and data persistence.
 */
public class FileItemRepositoryTest extends BaseTest {
    
    private ItemRepository itemRepository;
    
    /**
     * Sets up the test environment.
     * Creates a new FileItemRepository instance.
     */
    @Before
    public void setUpRepository() {
        // Override the data directory for testing
        System.setProperty("data.directory", TestDataUtil.TEST_DATA_DIR);
        itemRepository = new FileItemRepository();
    }
    
    /**
     * Tests saving an item.
     */
    @Test
    public void testSave() {
        // Create a test item
        Item item = TestDataUtil.createTestItem("I001");
        
        // Save the item
        Item savedItem = itemRepository.save(item);
        
        // Verify the saved item
        assertNotNull(savedItem);
        assertEquals(item.getCode(), savedItem.getCode());
        assertEquals(item.getName(), savedItem.getName());
        assertEquals(item.getPrice(), savedItem.getPrice(), 0.001);
        assertEquals(item.getCategory(), savedItem.getCategory());
        
        // Verify the item was persisted
        Optional<Item> retrievedItem = itemRepository.findByCode("I001");
        assertTrue(retrievedItem.isPresent());
        assertEquals(item.getName(), retrievedItem.get().getName());
    }
    
    /**
     * Tests finding an item by code.
     */
    @Test
    public void testFindByCode() {
        // Create and save test items
        Item item1 = TestDataUtil.createTestItem("I001");
        Item item2 = TestDataUtil.createTestItem("I002");
        
        itemRepository.save(item1);
        itemRepository.save(item2);
        
        // Find items by code
        Optional<Item> foundItem1 = itemRepository.findByCode("I001");
        Optional<Item> foundItem2 = itemRepository.findByCode("I002");
        Optional<Item> notFoundItem = itemRepository.findByCode("I999");
        
        // Verify the results
        assertTrue(foundItem1.isPresent());
        assertEquals("I001", foundItem1.get().getCode());
        
        assertTrue(foundItem2.isPresent());
        assertEquals("I002", foundItem2.get().getCode());
        
        assertFalse(notFoundItem.isPresent());
    }
    
    /**
     * Tests finding all items.
     */
    @Test
    public void testFindAll() {
        // Create and save test items
        List<Item> items = TestDataUtil.createTestItems(5);
        for (Item item : items) {
            itemRepository.save(item);
        }
        
        // Find all items
        List<Item> allItems = itemRepository.findAll();
        
        // Verify the results
        assertNotNull(allItems);
        assertEquals(5, allItems.size());
        
        // Verify the items are in the list
        for (int i = 0; i < 5; i++) {
            String expectedCode = String.format("T%03d", i + 1);
            boolean found = allItems.stream()
                    .anyMatch(item -> item.getCode().equals(expectedCode));
            assertTrue("Item with code " + expectedCode + " not found", found);
        }
    }
    
    /**
     * Tests updating an item.
     */
    @Test
    public void testUpdate() {
        // Create and save a test item
        Item item = TestDataUtil.createTestItem("I001");
        itemRepository.save(item);
        
        // Update the item
        item.setName("Updated Item");
        item.setPrice(15.0);
        item.setCategory("Updated Category");
        
        Item updatedItem = itemRepository.update(item);
        
        // Verify the updated item
        assertNotNull(updatedItem);
        assertEquals("Updated Item", updatedItem.getName());
        assertEquals(15.0, updatedItem.getPrice(), 0.001);
        assertEquals("Updated Category", updatedItem.getCategory());
        
        // Verify the item was persisted
        Optional<Item> retrievedItem = itemRepository.findByCode("I001");
        assertTrue(retrievedItem.isPresent());
        assertEquals("Updated Item", retrievedItem.get().getName());
        assertEquals(15.0, retrievedItem.get().getPrice(), 0.001);
        assertEquals("Updated Category", retrievedItem.get().getCategory());
    }
    
    /**
     * Tests deleting an item.
     */
    @Test
    public void testDelete() {
        // Create and save test items
        Item item1 = TestDataUtil.createTestItem("I001");
        Item item2 = TestDataUtil.createTestItem("I002");
        
        itemRepository.save(item1);
        itemRepository.save(item2);
        
        // Verify items exist
        assertTrue(itemRepository.findByCode("I001").isPresent());
        assertTrue(itemRepository.findByCode("I002").isPresent());
        
        // Delete an item
        boolean deleted = itemRepository.delete("I001");
        
        // Verify the result
        assertTrue(deleted);
        assertFalse(itemRepository.findByCode("I001").isPresent());
        assertTrue(itemRepository.findByCode("I002").isPresent());
        
        // Try to delete a non-existent item
        boolean notDeleted = itemRepository.delete("I999");
        assertFalse(notDeleted);
    }
}
