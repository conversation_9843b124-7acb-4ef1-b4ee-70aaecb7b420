package com.syos.test.util;

import com.syos.model.*;
import com.syos.util.DateUtil;
import com.syos.util.FileUtil;

import java.io.File;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for creating and managing test data.
 */
public class TestDataUtil {
    
    public static final String TEST_DATA_DIR = "test_data";
    
    /**
     * Creates a test item with the given code.
     * 
     * @param code The item code
     * @return A test item
     */
    public static Item createTestItem(String code) {
        return new Item(code, "Test Item " + code, 10.0, "Test Category");
    }
    
    /**
     * Creates a list of test items.
     * 
     * @param count The number of items to create
     * @return A list of test items
     */
    public static List<Item> createTestItems(int count) {
        List<Item> items = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            String code = String.format("T%03d", i);
            items.add(createTestItem(code));
        }
        return items;
    }
    
    /**
     * Creates a test stock batch for the given item.
     * 
     * @param item The item
     * @param stockType The stock type
     * @return A test stock batch
     */
    public static StockBatch createTestStockBatch(Item item, StockType stockType) {
        String batchId = "B" + item.getCode() + stockType.toString().charAt(0);
        LocalDate today = LocalDate.now();
        return new StockBatch(
                batchId,
                item,
                100,
                today.minusDays(10),
                today.plusMonths(6),
                stockType
        );
    }
    
    /**
     * Creates a test user.
     * 
     * @param userId The user ID
     * @param isOnlineCustomer Whether the user is an online customer
     * @return A test user
     */
    public static User createTestUser(String userId, boolean isOnlineCustomer) {
        return new User(
                userId,
                "user" + userId,
                "password" + userId,
                "Test User " + userId,
                "user" + userId + "@example.com",
                "123 Test St",
                "123-456-7890",
                isOnlineCustomer
        );
    }
    
    /**
     * Creates a test transaction.
     * 
     * @param transactionId The transaction ID
     * @param user The user (can be null for in-store transactions)
     * @param type The transaction type
     * @return A test transaction
     */
    public static Transaction createTestTransaction(String transactionId, User user, TransactionType type) {
        Transaction transaction = new Transaction(transactionId, user, type);
        return transaction;
    }
    
    /**
     * Adds test items to a transaction.
     * 
     * @param transaction The transaction
     * @param items The items to add
     * @param quantity The quantity of each item
     */
    public static void addItemsToTransaction(Transaction transaction, List<Item> items, int quantity) {
        for (Item item : items) {
            transaction.addItem(item, quantity);
        }
        transaction.calculateTotal();
    }
    
    /**
     * Creates a test bill for a transaction.
     * 
     * @param billNumber The bill number
     * @param transaction The transaction
     * @return A test bill
     */
    public static Bill createTestBill(String billNumber, Transaction transaction) {
        return new Bill(billNumber, transaction);
    }
    
    /**
     * Sets up the test data directory.
     */
    public static void setupTestDataDirectory() {
        FileUtil.ensureDirectoryExists(TEST_DATA_DIR);
    }
    
    /**
     * Cleans up the test data directory.
     */
    public static void cleanupTestDataDirectory() {
        deleteDirectory(new File(TEST_DATA_DIR));
    }
    
    /**
     * Recursively deletes a directory.
     * 
     * @param directory The directory to delete
     * @return true if successful, false otherwise
     */
    private static boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }
}
