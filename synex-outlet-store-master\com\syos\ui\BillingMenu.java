package com.syos.ui;

import com.syos.model.*;
import com.syos.service.BillingService;
import com.syos.service.InventoryService;
import com.syos.service.UserService;

import java.util.List;

import java.util.Optional;
import java.util.Scanner;

/**
 * Menu for billing operations.
 */
public class BillingMenu {

    private final Scanner scanner;
    private final BillingService billingService;
    private final InventoryService inventoryService;
    private final UserService userService;

    public BillingMenu(Scanner scanner,
                       BillingService billingService,
                       InventoryService inventoryService,
                       UserService userService) {
        this.scanner = scanner;
        this.billingService = billingService;
        this.inventoryService = inventoryService;
        this.userService = userService;
    }

    /**
     * Displays the billing menu and handles user input.
     *
     * @param currentUser The currently logged-in user (may be null)
     */
    public void displayMenu(User currentUser) {
        boolean back = false;

        while (!back) {
            System.out.println("\n===== Billing Operations =====");
            System.out.println("1. New In-Store Transaction");
            System.out.println("2. New Online Transaction");
            System.out.println("3. View All Transactions");
            System.out.println("4. Back to Main Menu");
            System.out.print("Enter your choice: ");

            int choice;
            try {
                choice = Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Invalid input. Please enter a number.");
                continue;
            }

            switch (choice) {
                case 1:
                    processInStoreTransaction();
                    break;
                case 2:
                    processOnlineTransaction(currentUser);
                    break;
                case 3:
                    viewAllTransactions();
                    break;
                case 4:
                    back = true;
                    break;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        }
    }

    /**
     * Processes an in-store transaction.
     */
    private void processInStoreTransaction() {
        Transaction transaction = billingService.createTransaction(null, TransactionType.IN_STORE);

        System.out.println("\n===== New In-Store Transaction =====");
        System.out.println("Transaction ID: " + transaction.getTransactionId());

        boolean addingItems = true;

        while (addingItems) {
            System.out.print("Enter item code (or 'done' to finish): ");
            String itemCode = scanner.nextLine().trim();

            if (itemCode.equalsIgnoreCase("done")) {
                addingItems = false;
                continue;
            }

            Optional<Item> itemOpt = inventoryService.findItemByCode(itemCode);
            if (!itemOpt.isPresent()) {
                System.out.println("Item not found. Please try again.");
                continue;
            }

            Item item = itemOpt.get();
            System.out.println("Item: " + item.getName() + " - $" + item.getPrice());

            System.out.print("Enter quantity: ");
            int quantity;
            try {
                quantity = Integer.parseInt(scanner.nextLine().trim());
                if (quantity <= 0) {
                    System.out.println("Quantity must be positive. Please try again.");
                    continue;
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid input. Please enter a number.");
                continue;
            }

            if (!inventoryService.hasSufficientStock(itemCode, quantity, StockType.SHELF)) {
                System.out.println("Insufficient stock. Available: " +
                        inventoryService.getTotalQuantity(itemCode, StockType.SHELF));
                continue;
            }

            billingService.addItemToTransaction(transaction, itemCode, quantity);
            System.out.println("Item added to cart.");
        }

        if (transaction.getItems().isEmpty()) {
            System.out.println("Transaction cancelled - no items added.");
            return;
        }

        // Calculate total
        double total = billingService.calculateTotal(transaction);
        System.out.println("\nSubtotal: $" + String.format("%.2f", total));

        // Apply discount if any
        System.out.print("Enter discount percentage (0-100): ");
        double discountPercentage;
        try {
            discountPercentage = Double.parseDouble(scanner.nextLine().trim());
            if (discountPercentage < 0 || discountPercentage > 100) {
                System.out.println("Invalid discount. Using 0%.");
                discountPercentage = 0;
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid input. Using 0% discount.");
            discountPercentage = 0;
        }

        billingService.applyDiscount(transaction, discountPercentage);

        if (discountPercentage > 0) {
            System.out.println("Discount: $" + String.format("%.2f", transaction.getDiscountAmount()));
        }

        System.out.println("Final Amount: $" + String.format("%.2f", transaction.getFinalAmount()));

        // Process payment
        System.out.print("Enter cash tendered: $");
        double cashTendered;
        try {
            cashTendered = Double.parseDouble(scanner.nextLine().trim());
            if (cashTendered < transaction.getFinalAmount()) {
                System.out.println("Insufficient payment. Transaction cancelled.");
                return;
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid input. Transaction cancelled.");
            return;
        }

        double change = billingService.processPayment(transaction, cashTendered);
        System.out.println("Change: $" + String.format("%.2f", change));

        // Complete transaction and generate bill
        Bill bill = billingService.completeTransaction(transaction);
        System.out.println("\n" + bill.generateFormattedBill());
        System.out.println("Transaction completed successfully.");
    }

    /**
     * Processes an online transaction.
     *
     * @param currentUser The currently logged-in user
     */
    private void processOnlineTransaction(User currentUser) {
        if (currentUser == null || !currentUser.isOnlineCustomer()) {
            System.out.println("You must be logged in as an online customer to make online purchases.");
            return;
        }

        Transaction transaction = billingService.createTransaction(currentUser, TransactionType.ONLINE);

        System.out.println("\n===== New Online Transaction =====");
        System.out.println("Transaction ID: " + transaction.getTransactionId());
        System.out.println("Customer: " + currentUser.getFullName());

        boolean addingItems = true;

        while (addingItems) {
            System.out.print("Enter item code (or 'done' to finish): ");
            String itemCode = scanner.nextLine().trim();

            if (itemCode.equalsIgnoreCase("done")) {
                addingItems = false;
                continue;
            }

            Optional<Item> itemOpt = inventoryService.findItemByCode(itemCode);
            if (!itemOpt.isPresent()) {
                System.out.println("Item not found. Please try again.");
                continue;
            }

            Item item = itemOpt.get();
            System.out.println("Item: " + item.getName() + " - $" + item.getPrice());

            System.out.print("Enter quantity: ");
            int quantity;
            try {
                quantity = Integer.parseInt(scanner.nextLine().trim());
                if (quantity <= 0) {
                    System.out.println("Quantity must be positive. Please try again.");
                    continue;
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid input. Please enter a number.");
                continue;
            }

            if (!inventoryService.hasSufficientStock(itemCode, quantity, StockType.ONLINE)) {
                System.out.println("Insufficient stock. Available: " +
                        inventoryService.getTotalQuantity(itemCode, StockType.ONLINE));
                continue;
            }

            billingService.addItemToTransaction(transaction, itemCode, quantity);
            System.out.println("Item added to cart.");
        }

        if (transaction.getItems().isEmpty()) {
            System.out.println("Transaction cancelled - no items added.");
            return;
        }

        // Calculate total
        double total = billingService.calculateTotal(transaction);
        System.out.println("\nSubtotal: $" + String.format("%.2f", total));

        // Apply discount if any (online customers get 5% discount)
        billingService.applyDiscount(transaction, 5);

        System.out.println("Online Discount (5%): $" + String.format("%.2f", transaction.getDiscountAmount()));
        System.out.println("Final Amount: $" + String.format("%.2f", transaction.getFinalAmount()));

        // Process payment (simplified for online)
        System.out.println("Processing online payment...");
        billingService.processPayment(transaction, transaction.getFinalAmount());

        // Complete transaction and generate bill
        Bill bill = billingService.completeTransaction(transaction);
        System.out.println("\n" + bill.generateFormattedBill());
        System.out.println("Transaction completed successfully.");
    }

    /**
     * Displays all transactions.
     */
    private void viewAllTransactions() {
        System.out.println("\n===== All Transactions =====");

        List<Transaction> transactions = billingService.getAllTransactions();

        if (transactions.isEmpty()) {
            System.out.println("No transactions found.");
            return;
        }

        for (Transaction transaction : transactions) {
            System.out.println("ID: " + transaction.getTransactionId() +
                    " | Date: " + transaction.getDateTime() +
                    " | Type: " + transaction.getType() +
                    " | Items: " + transaction.getItems().size() +
                    " | Amount: $" + String.format("%.2f", transaction.getFinalAmount()));
        }
    }
}
