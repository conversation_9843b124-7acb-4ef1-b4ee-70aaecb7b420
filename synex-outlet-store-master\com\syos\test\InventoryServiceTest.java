package com.syos.test;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.repository.ItemRepository;
import com.syos.repository.StockRepository;
import com.syos.repository.impl.FileItemRepository;
import com.syos.repository.impl.FileStockRepository;
import com.syos.service.InventoryService;
import com.syos.util.FileUtil;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Test class for the InventoryService.
 */
public class InventoryServiceTest {
    
    private static final String TEST_DATA_DIRECTORY = "test_data";
    
    public static void main(String[] args) {
        System.out.println("Running InventoryService tests...");
        
        // Ensure test data directory exists
        FileUtil.ensureDirectoryExists(TEST_DATA_DIRECTORY);
        
        // Initialize repositories and service
        ItemRepository itemRepository = new FileItemRepository();
        StockRepository stockRepository = new FileStockRepository();
        InventoryService inventoryService = new InventoryService(itemRepository, stockRepository);
        
        // Run tests
        testAddItem(inventoryService);
        testAddStockBatch(inventoryService);
        testReduceStock(inventoryService);
        testMoveFromStorageToShelf(inventoryService);
        
        System.out.println("All tests completed.");
    }
    
    /**
     * Tests adding an item.
     * 
     * @param inventoryService The inventory service
     */
    private static void testAddItem(InventoryService inventoryService) {
        System.out.println("\nTesting addItem...");
        
        // Create a test item
        Item item = new Item("T001", "Test Item", 9.99, "Test");
        
        // Add the item
        Item addedItem = inventoryService.addItem(item);
        
        // Verify the item was added
        Optional<Item> retrievedItem = inventoryService.findItemByCode("T001");
        
        if (retrievedItem.isPresent() && retrievedItem.get().getName().equals("Test Item")) {
            System.out.println("addItem test passed.");
        } else {
            System.out.println("addItem test failed.");
        }
    }
    
    /**
     * Tests adding a stock batch.
     * 
     * @param inventoryService The inventory service
     */
    private static void testAddStockBatch(InventoryService inventoryService) {
        System.out.println("\nTesting addStockBatch...");
        
        // Get the test item
        Optional<Item> itemOpt = inventoryService.findItemByCode("T001");
        
        if (!itemOpt.isPresent()) {
            System.out.println("addStockBatch test failed: Test item not found.");
            return;
        }
        
        Item item = itemOpt.get();
        
        // Create a test stock batch
        LocalDate today = LocalDate.now();
        StockBatch stockBatch = new StockBatch(
                "TB001",
                item,
                50,
                today,
                today.plusMonths(6),
                StockType.SHELF
        );
        
        // Add the stock batch
        StockBatch addedBatch = inventoryService.addStockBatch(stockBatch);
        
        // Verify the batch was added
        List<StockBatch> batches = inventoryService.getStockBatchesByItemCode("T001");
        
        if (!batches.isEmpty() && batches.get(0).getBatchId().equals("TB001")) {
            System.out.println("addStockBatch test passed.");
        } else {
            System.out.println("addStockBatch test failed.");
        }
    }
    
    /**
     * Tests reducing stock.
     * 
     * @param inventoryService The inventory service
     */
    private static void testReduceStock(InventoryService inventoryService) {
        System.out.println("\nTesting reduceStock...");
        
        // Get the initial quantity
        int initialQuantity = inventoryService.getTotalQuantity("T001", StockType.SHELF);
        
        // Reduce the stock
        boolean result = inventoryService.reduceStock("T001", 10, StockType.SHELF);
        
        // Get the updated quantity
        int updatedQuantity = inventoryService.getTotalQuantity("T001", StockType.SHELF);
        
        if (result && updatedQuantity == initialQuantity - 10) {
            System.out.println("reduceStock test passed.");
        } else {
            System.out.println("reduceStock test failed.");
        }
    }
    
    /**
     * Tests moving items from storage to shelf.
     * 
     * @param inventoryService The inventory service
     */
    private static void testMoveFromStorageToShelf(InventoryService inventoryService) {
        System.out.println("\nTesting moveFromStorageToShelf...");
        
        // Get the test item
        Optional<Item> itemOpt = inventoryService.findItemByCode("T001");
        
        if (!itemOpt.isPresent()) {
            System.out.println("moveFromStorageToShelf test failed: Test item not found.");
            return;
        }
        
        Item item = itemOpt.get();
        
        // Create a storage batch
        LocalDate today = LocalDate.now();
        StockBatch storageBatch = new StockBatch(
                "TB002",
                item,
                30,
                today,
                today.plusMonths(6),
                StockType.STORAGE
        );
        
        // Add the storage batch
        inventoryService.addStockBatch(storageBatch);
        
        // Get initial quantities
        int initialShelfQuantity = inventoryService.getTotalQuantity("T001", StockType.SHELF);
        int initialStorageQuantity = inventoryService.getTotalQuantity("T001", StockType.STORAGE);
        
        // Move items from storage to shelf
        boolean result = inventoryService.moveFromStorageToShelf("T001", 20);
        
        // Get updated quantities
        int updatedShelfQuantity = inventoryService.getTotalQuantity("T001", StockType.SHELF);
        int updatedStorageQuantity = inventoryService.getTotalQuantity("T001", StockType.STORAGE);
        
        if (result && 
                updatedShelfQuantity == initialShelfQuantity + 20 && 
                updatedStorageQuantity == initialStorageQuantity - 20) {
            System.out.println("moveFromStorageToShelf test passed.");
        } else {
            System.out.println("moveFromStorageToShelf test failed.");
        }
    }
}
