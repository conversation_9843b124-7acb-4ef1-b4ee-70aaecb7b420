package com.syos.repository.impl;

import com.syos.model.*;
import com.syos.repository.ItemRepository;
import com.syos.repository.TransactionRepository;
import com.syos.repository.UserRepository;
import com.syos.util.DateUtil;
import com.syos.util.FileUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * File-based implementation of the TransactionRepository interface.
 */
public class FileTransactionRepository implements TransactionRepository {
    
    private static final String DATA_DIRECTORY = "data";
    private static final String TRANSACTIONS_FILE = DATA_DIRECTORY + "/transactions.csv";
    private static final String TRANSACTION_ITEMS_FILE = DATA_DIRECTORY + "/transaction_items.csv";
    private static final String DELIMITER = ",";
    
    private final ItemRepository itemRepository = new FileItemRepository();
    private final UserRepository userRepository = new FileUserRepository();
    
    /**
     * Converts a Transaction object to a CSV line.
     * 
     * @param transaction The transaction to convert
     * @return The CSV line
     */
    private String transactionToCsvLine(Transaction transaction) {
        String userId = transaction.getUser() != null ? transaction.getUser().getUserId() : "";
        
        return String.join(DELIMITER,
                transaction.getTransactionId(),
                DateUtil.formatDateTime(transaction.getDateTime()),
                userId,
                String.valueOf(transaction.getTotalAmount()),
                String.valueOf(transaction.getDiscountAmount()),
                String.valueOf(transaction.getFinalAmount()),
                String.valueOf(transaction.getCashTendered()),
                String.valueOf(transaction.getChange()),
                transaction.getType().toString());
    }
    
    /**
     * Converts a CartItem object to a CSV line.
     * 
     * @param transactionId The transaction ID
     * @param cartItem The cart item to convert
     * @return The CSV line
     */
    private String cartItemToCsvLine(String transactionId, CartItem cartItem) {
        return String.join(DELIMITER,
                transactionId,
                cartItem.getItem().getCode(),
                String.valueOf(cartItem.getQuantity()));
    }
    
    /**
     * Converts a CSV line to a Transaction object.
     * 
     * @param line The CSV line
     * @return The Transaction object, or null if conversion fails
     */
    private Transaction csvLineToTransaction(String line) {
        String[] parts = line.split(DELIMITER);
        if (parts.length >= 9) {
            String transactionId = parts[0];
            LocalDateTime dateTime = DateUtil.parseDateTime(parts[1]);
            String userId = parts[2];
            double totalAmount = Double.parseDouble(parts[3]);
            double discountAmount = Double.parseDouble(parts[4]);
            double finalAmount = Double.parseDouble(parts[5]);
            double cashTendered = Double.parseDouble(parts[6]);
            double change = Double.parseDouble(parts[7]);
            TransactionType type = TransactionType.valueOf(parts[8]);
            
            User user = null;
            if (!userId.isEmpty()) {
                Optional<User> userOpt = userRepository.findById(userId);
                if (userOpt.isPresent()) {
                    user = userOpt.get();
                }
            }
            
            Transaction transaction = new Transaction(transactionId, user, type);
            transaction.setDateTime(dateTime);
            transaction.setTotalAmount(totalAmount);
            transaction.setDiscountAmount(discountAmount);
            transaction.setFinalAmount(finalAmount);
            transaction.setCashTendered(cashTendered);
            transaction.setChange(change);
            
            // Load transaction items
            List<CartItem> items = loadTransactionItems(transactionId);
            transaction.setItems(items);
            
            return transaction;
        }
        return null;
    }
    
    /**
     * Loads the items for a transaction.
     * 
     * @param transactionId The transaction ID
     * @return A list of cart items
     */
    private List<CartItem> loadTransactionItems(String transactionId) {
        List<String> lines = FileUtil.readLinesFromFile(TRANSACTION_ITEMS_FILE);
        List<CartItem> items = new ArrayList<>();
        
        for (String line : lines) {
            String[] parts = line.split(DELIMITER);
            if (parts.length >= 3 && parts[0].equals(transactionId)) {
                String itemCode = parts[1];
                int quantity = Integer.parseInt(parts[2]);
                
                Optional<Item> itemOpt = itemRepository.findByCode(itemCode);
                if (itemOpt.isPresent()) {
                    CartItem cartItem = new CartItem(itemOpt.get(), quantity);
                    items.add(cartItem);
                }
            }
        }
        
        return items;
    }
    
    @Override
    public Transaction save(Transaction transaction) {
        List<Transaction> transactions = findAll();
        
        // Check if transaction with the same ID already exists
        Optional<Transaction> existingTransaction = transactions.stream()
                .filter(t -> t.getTransactionId().equals(transaction.getTransactionId()))
                .findFirst();
        
        if (existingTransaction.isPresent()) {
            // Update existing transaction
            return update(transaction);
        } else {
            // Add new transaction
            List<String> transactionLines = transactions.stream()
                    .map(this::transactionToCsvLine)
                    .collect(Collectors.toList());
            
            transactionLines.add(transactionToCsvLine(transaction));
            
            FileUtil.writeLinesToFile(TRANSACTIONS_FILE, transactionLines);
            
            // Save transaction items
            List<String> itemLines = new ArrayList<>();
            for (CartItem item : transaction.getItems()) {
                itemLines.add(cartItemToCsvLine(transaction.getTransactionId(), item));
            }
            
            // Append to existing items file
            for (String line : itemLines) {
                FileUtil.appendLineToFile(TRANSACTION_ITEMS_FILE, line);
            }
            
            return transaction;
        }
    }
    
    @Override
    public Optional<Transaction> findById(String transactionId) {
        return findAll().stream()
                .filter(transaction -> transaction.getTransactionId().equals(transactionId))
                .findFirst();
    }
    
    @Override
    public List<Transaction> findByUserId(String userId) {
        return findAll().stream()
                .filter(transaction -> transaction.getUser() != null && 
                        transaction.getUser().getUserId().equals(userId))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Transaction> findByType(TransactionType type) {
        return findAll().stream()
                .filter(transaction -> transaction.getType() == type)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Transaction> findByDate(LocalDate date) {
        return findAll().stream()
                .filter(transaction -> transaction.getDateTime().toLocalDate().equals(date))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Transaction> findAll() {
        List<String> lines = FileUtil.readLinesFromFile(TRANSACTIONS_FILE);
        List<Transaction> transactions = new ArrayList<>();
        
        for (String line : lines) {
            Transaction transaction = csvLineToTransaction(line);
            if (transaction != null) {
                transactions.add(transaction);
            }
        }
        
        return transactions;
    }
    
    @Override
    public boolean delete(String transactionId) {
        List<Transaction> transactions = findAll();
        List<Transaction> updatedTransactions = transactions.stream()
                .filter(transaction -> !transaction.getTransactionId().equals(transactionId))
                .collect(Collectors.toList());
        
        if (updatedTransactions.size() < transactions.size()) {
            List<String> lines = updatedTransactions.stream()
                    .map(this::transactionToCsvLine)
                    .collect(Collectors.toList());
            
            boolean transactionDeleted = FileUtil.writeLinesToFile(TRANSACTIONS_FILE, lines);
            
            // Delete transaction items
            List<String> itemLines = FileUtil.readLinesFromFile(TRANSACTION_ITEMS_FILE);
            List<String> updatedItemLines = itemLines.stream()
                    .filter(line -> !line.startsWith(transactionId + DELIMITER))
                    .collect(Collectors.toList());
            
            boolean itemsDeleted = FileUtil.writeLinesToFile(TRANSACTION_ITEMS_FILE, updatedItemLines);
            
            return transactionDeleted && itemsDeleted;
        }
        
        return false;
    }
    
    @Override
    public String getNextTransactionId() {
        List<Transaction> transactions = findAll();
        int maxId = 0;
        
        for (Transaction transaction : transactions) {
            try {
                int id = Integer.parseInt(transaction.getTransactionId().substring(1));
                if (id > maxId) {
                    maxId = id;
                }
            } catch (NumberFormatException | IndexOutOfBoundsException e) {
                // Ignore invalid IDs
            }
        }
        
        return "T" + String.format("%04d", maxId + 1);
    }
    
    /**
     * Updates an existing transaction.
     * 
     * @param transaction The transaction to update
     * @return The updated transaction
     */
    private Transaction update(Transaction transaction) {
        delete(transaction.getTransactionId());
        return save(transaction);
    }
}
