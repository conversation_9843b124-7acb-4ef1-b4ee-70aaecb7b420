package com.syos.service;

import com.syos.model.*;
import com.syos.repository.BillRepository;
import com.syos.repository.ItemRepository;
import com.syos.repository.TransactionRepository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service class for billing operations.
 */
public class BillingService {
    
    private final ItemRepository itemRepository;
    private final TransactionRepository transactionRepository;
    private final BillRepository billRepository;
    private final InventoryService inventoryService;
    
    public BillingService(ItemRepository itemRepository, 
                          TransactionRepository transactionRepository,
                          BillRepository billRepository,
                          InventoryService inventoryService) {
        this.itemRepository = itemRepository;
        this.transactionRepository = transactionRepository;
        this.billRepository = billRepository;
        this.inventoryService = inventoryService;
    }
    
    /**
     * Creates a new transaction.
     * 
     * @param user The user (null for in-store transactions without registered users)
     * @param type The transaction type
     * @return The created transaction
     */
    public Transaction createTransaction(User user, TransactionType type) {
        String transactionId = transactionRepository.getNextTransactionId();
        Transaction transaction = new Transaction(transactionId, user, type);
        return transaction;
    }
    
    /**
     * Adds an item to a transaction.
     * 
     * @param transaction The transaction
     * @param itemCode The item code
     * @param quantity The quantity
     * @return true if successful, false otherwise
     */
    public boolean addItemToTransaction(Transaction transaction, String itemCode, int quantity) {
        Optional<Item> itemOpt = itemRepository.findByCode(itemCode);
        if (!itemOpt.isPresent()) {
            return false;
        }
        
        Item item = itemOpt.get();
        StockType stockType = transaction.getType() == TransactionType.ONLINE 
                ? StockType.ONLINE : StockType.SHELF;
        
        if (!inventoryService.hasSufficientStock(itemCode, quantity, stockType)) {
            return false;
        }
        
        transaction.addItem(item, quantity);
        return true;
    }
    
    /**
     * Calculates the total amount for a transaction.
     * 
     * @param transaction The transaction
     * @return The total amount
     */
    public double calculateTotal(Transaction transaction) {
        return transaction.calculateTotal();
    }
    
    /**
     * Applies a discount to a transaction.
     * 
     * @param transaction The transaction
     * @param discountPercentage The discount percentage (0-100)
     */
    public void applyDiscount(Transaction transaction, double discountPercentage) {
        transaction.applyDiscount(discountPercentage);
    }
    
    /**
     * Processes payment for a transaction.
     * 
     * @param transaction The transaction
     * @param cashTendered The amount of cash provided
     * @return The change to be returned, or -1 if payment is insufficient
     */
    public double processPayment(Transaction transaction, double cashTendered) {
        return transaction.processPayment(cashTendered);
    }
    
    /**
     * Completes a transaction and generates a bill.
     * 
     * @param transaction The transaction
     * @return The generated bill
     */
    public Bill completeTransaction(Transaction transaction) {
        // Save the transaction
        transactionRepository.save(transaction);
        
        // Update inventory
        for (CartItem cartItem : transaction.getItems()) {
            StockType stockType = transaction.getType() == TransactionType.ONLINE 
                    ? StockType.ONLINE : StockType.SHELF;
            
            inventoryService.reduceStock(
                    cartItem.getItem().getCode(), 
                    cartItem.getQuantity(), 
                    stockType
            );
        }
        
        // Generate bill
        String billNumber = billRepository.getNextBillNumber();
        Bill bill = new Bill(billNumber, transaction);
        billRepository.save(bill);
        
        return bill;
    }
    
    /**
     * Gets all transactions.
     * 
     * @return A list of all transactions
     */
    public List<Transaction> getAllTransactions() {
        return transactionRepository.findAll();
    }
    
    /**
     * Gets all bills.
     * 
     * @return A list of all bills
     */
    public List<Bill> getAllBills() {
        return billRepository.findAll();
    }
}
