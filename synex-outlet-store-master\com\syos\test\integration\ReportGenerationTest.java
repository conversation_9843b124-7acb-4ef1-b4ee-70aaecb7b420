package com.syos.test.integration;

import com.syos.model.*;
import com.syos.repository.*;
import com.syos.repository.impl.*;
import com.syos.service.InventoryService;
import com.syos.service.ReportService;
import com.syos.test.base.BaseTest;
import com.syos.test.util.TestDataUtil;
import com.syos.util.FileUtil;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.Assert.*;

/**
 * Integration test for the report generation functionality.
 * Tests the generation of all required reports based on test data.
 */
public class ReportGenerationTest extends BaseTest {
    
    private ItemRepository itemRepository;
    private StockRepository stockRepository;
    private TransactionRepository transactionRepository;
    private BillRepository billRepository;
    private InventoryService inventoryService;
    private ReportService reportService;
    
    /**
     * Sets up the test environment.
     * Initializes repositories and services, and creates test data.
     */
    @Before
    public void setUpWorkflow() {
        // Override the data directory for testing
        System.setProperty("data.directory", TestDataUtil.TEST_DATA_DIR);
        
        // Initialize repositories
        itemRepository = new FileItemRepository();
        stockRepository = new FileStockRepository();
        transactionRepository = new FileTransactionRepository();
        billRepository = new FileBillRepository();
        
        // Initialize services
        inventoryService = new InventoryService(itemRepository, stockRepository);
        reportService = new ReportService(
                itemRepository, stockRepository, transactionRepository, billRepository, inventoryService);
        
        // Create test data
        createTestData();
    }
    
    /**
     * Creates test data for report generation.
     */
    private void createTestData() {
        // Create test items
        Item rice = new Item("I001", "Rice", 2.50, "Groceries");
        Item flour = new Item("I002", "Flour", 1.80, "Groceries");
        Item sugar = new Item("I003", "Sugar", 1.50, "Groceries");
        Item milk = new Item("I004", "Milk", 1.20, "Dairy");
        Item eggs = new Item("I005", "Eggs", 2.00, "Dairy");
        
        itemRepository.save(rice);
        itemRepository.save(flour);
        itemRepository.save(sugar);
        itemRepository.save(milk);
        itemRepository.save(eggs);
        
        // Create stock batches
        LocalDate today = LocalDate.now();
        
        // Shelf stock
        createStockBatch("SB001", rice, 20, today, 6, StockType.SHELF);
        createStockBatch("SB002", flour, 15, today, 5, StockType.SHELF);
        createStockBatch("SB003", sugar, 10, today, 4, StockType.SHELF);
        createStockBatch("SB004", milk, 5, today, 1, StockType.SHELF);
        createStockBatch("SB005", eggs, 8, today, 2, StockType.SHELF);
        
        // Storage stock
        createStockBatch("ST001", rice, 80, today, 8, StockType.STORAGE);
        createStockBatch("ST002", flour, 60, today, 7, StockType.STORAGE);
        createStockBatch("ST003", sugar, 40, today, 6, StockType.STORAGE);
        createStockBatch("ST004", milk, 20, today, 2, StockType.STORAGE);
        createStockBatch("ST005", eggs, 12, today, 3, StockType.STORAGE);
        
        // Online stock
        createStockBatch("ON001", rice, 30, today, 7, StockType.ONLINE);
        createStockBatch("ON002", flour, 25, today, 6, StockType.ONLINE);
        createStockBatch("ON003", sugar, 20, today, 5, StockType.ONLINE);
        createStockBatch("ON004", milk, 15, today, 1, StockType.ONLINE);
        createStockBatch("ON005", eggs, 10, today, 2, StockType.ONLINE);
        
        // Create transactions
        LocalDate yesterday = today.minusDays(1);
        
        // In-store transactions
        Transaction inStoreTransaction1 = createTransaction("T001", null, TransactionType.IN_STORE, yesterday);
        inStoreTransaction1.addItem(rice, 3);
        inStoreTransaction1.addItem(flour, 2);
        inStoreTransaction1.calculateTotal();
        inStoreTransaction1.processPayment(15.0);
        
        Transaction inStoreTransaction2 = createTransaction("T002", null, TransactionType.IN_STORE, today);
        inStoreTransaction2.addItem(sugar, 4);
        inStoreTransaction2.addItem(milk, 2);
        inStoreTransaction2.calculateTotal();
        inStoreTransaction2.processPayment(10.0);
        
        // Online transaction
        User user = new User("U001", "testuser", "password", "Test User", 
                "<EMAIL>", "123 Test St", "123-456-7890", true);
        
        Transaction onlineTransaction = createTransaction("T003", user, TransactionType.ONLINE, today);
        onlineTransaction.addItem(rice, 2);
        onlineTransaction.addItem(eggs, 1);
        onlineTransaction.calculateTotal();
        onlineTransaction.processPayment(7.0);
        
        transactionRepository.save(inStoreTransaction1);
        transactionRepository.save(inStoreTransaction2);
        transactionRepository.save(onlineTransaction);
        
        // Create bills
        Bill bill1 = new Bill("B001", inStoreTransaction1);
        Bill bill2 = new Bill("B002", inStoreTransaction2);
        Bill bill3 = new Bill("B003", onlineTransaction);
        
        billRepository.save(bill1);
        billRepository.save(bill2);
        billRepository.save(bill3);
    }
    
    /**
     * Creates a stock batch with the given parameters.
     */
    private void createStockBatch(String batchId, Item item, int quantity, LocalDate today, 
                                 int expiryMonths, StockType stockType) {
        StockBatch batch = new StockBatch(
                batchId,
                item,
                quantity,
                today.minusDays(10),
                today.plusMonths(expiryMonths),
                stockType
        );
        stockRepository.save(batch);
    }
    
    /**
     * Creates a transaction with the given parameters.
     */
    private Transaction createTransaction(String transactionId, User user, 
                                         TransactionType type, LocalDate date) {
        Transaction transaction = new Transaction(transactionId, user, type);
        transaction.setDateTime(LocalDateTime.of(
                date.getYear(), date.getMonth(), date.getDayOfMonth(), 12, 0));
        return transaction;
    }
    
    /**
     * Tests generating a daily sales report.
     */
    @Test
    public void testDailySalesReport() {
        // Generate the report for today
        LocalDate today = LocalDate.now();
        String reportPath = reportService.generateDailySalesReport(today);
        
        // Verify the report was generated
        File reportFile = new File(reportPath);
        assertTrue(reportFile.exists());
        
        // Read the report content
        List<String> lines = FileUtil.readLinesFromFile(reportPath);
        assertFalse(lines.isEmpty());
        
        // Verify the report contains the expected data
        String content = String.join("\n", lines);
        assertTrue(content.contains("Daily Sales Report"));
        assertTrue(content.contains("Sugar"));
        assertTrue(content.contains("Milk"));
        assertTrue(content.contains("Rice"));
        assertTrue(content.contains("Eggs"));
    }
    
    /**
     * Tests generating a reshelf report.
     */
    @Test
    public void testReshelfReport() {
        // Generate the report
        String reportPath = reportService.generateReshelfReport();
        
        // Verify the report was generated
        File reportFile = new File(reportPath);
        assertTrue(reportFile.exists());
        
        // Read the report content
        List<String> lines = FileUtil.readLinesFromFile(reportPath);
        assertFalse(lines.isEmpty());
        
        // Verify the report contains the expected data
        String content = String.join("\n", lines);
        assertTrue(content.contains("Items to be Reshelved Report"));
        
        // Milk has low shelf quantity (5) and should be in the report
        assertTrue(content.contains("Milk"));
    }
    
    /**
     * Tests generating a reorder report.
     */
    @Test
    public void testReorderReport() {
        // Generate the report
        String reportPath = reportService.generateReorderReport();
        
        // Verify the report was generated
        File reportFile = new File(reportPath);
        assertTrue(reportFile.exists());
        
        // Read the report content
        List<String> lines = FileUtil.readLinesFromFile(reportPath);
        assertFalse(lines.isEmpty());
        
        // Verify the report contains the expected data
        String content = String.join("\n", lines);
        assertTrue(content.contains("Items to Reorder Report"));
        
        // Milk has total quantity < 50 (5 + 20 + 15 = 40) and should be in the report
        assertTrue(content.contains("Milk"));
    }
    
    /**
     * Tests generating a full stock report.
     */
    @Test
    public void testFullStockReport() {
        // Generate the report
        String reportPath = reportService.generateFullStockReport();
        
        // Verify the report was generated
        File reportFile = new File(reportPath);
        assertTrue(reportFile.exists());
        
        // Read the report content
        List<String> lines = FileUtil.readLinesFromFile(reportPath);
        assertFalse(lines.isEmpty());
        
        // Verify the report contains the expected data
        String content = String.join("\n", lines);
        assertTrue(content.contains("Full Stock Report"));
        assertTrue(content.contains("Rice"));
        assertTrue(content.contains("Flour"));
        assertTrue(content.contains("Sugar"));
        assertTrue(content.contains("Milk"));
        assertTrue(content.contains("Eggs"));
        assertTrue(content.contains("SHELF"));
        assertTrue(content.contains("STORAGE"));
        assertTrue(content.contains("ONLINE"));
    }
    
    /**
     * Tests generating a bill report.
     */
    @Test
    public void testBillReport() {
        // Generate the report
        String reportPath = reportService.generateBillReport();
        
        // Verify the report was generated
        File reportFile = new File(reportPath);
        assertTrue(reportFile.exists());
        
        // Read the report content
        List<String> lines = FileUtil.readLinesFromFile(reportPath);
        assertFalse(lines.isEmpty());
        
        // Verify the report contains the expected data
        String content = String.join("\n", lines);
        assertTrue(content.contains("Bill Report"));
        assertTrue(content.contains("B001"));
        assertTrue(content.contains("B002"));
        assertTrue(content.contains("B003"));
        assertTrue(content.contains("IN_STORE"));
        assertTrue(content.contains("ONLINE"));
    }
}
