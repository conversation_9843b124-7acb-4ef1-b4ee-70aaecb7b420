package com.syos.service;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.repository.ItemRepository;
import com.syos.repository.StockRepository;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service class for inventory management operations.
 */
public class InventoryService {
    
    private final ItemRepository itemRepository;
    private final StockRepository stockRepository;
    
    public InventoryService(ItemRepository itemRepository, StockRepository stockRepository) {
        this.itemRepository = itemRepository;
        this.stockRepository = stockRepository;
    }
    
    /**
     * Adds a new item to the inventory.
     * 
     * @param item The item to add
     * @return The added item
     */
    public Item addItem(Item item) {
        return itemRepository.save(item);
    }
    
    /**
     * Updates an existing item in the inventory.
     * 
     * @param item The item to update
     * @return The updated item
     */
    public Item updateItem(Item item) {
        return itemRepository.update(item);
    }
    
    /**
     * Finds an item by its code.
     * 
     * @param code The item code
     * @return An Optional containing the item if found, empty otherwise
     */
    public Optional<Item> findItemByCode(String code) {
        return itemRepository.findByCode(code);
    }
    
    /**
     * Gets all items in the inventory.
     * 
     * @return A list of all items
     */
    public List<Item> getAllItems() {
        return itemRepository.findAll();
    }
    
    /**
     * Adds a new stock batch.
     * 
     * @param stockBatch The stock batch to add
     * @return The added stock batch
     */
    public StockBatch addStockBatch(StockBatch stockBatch) {
        return stockRepository.save(stockBatch);
    }
    
    /**
     * Updates an existing stock batch.
     * 
     * @param stockBatch The stock batch to update
     * @return The updated stock batch
     */
    public StockBatch updateStockBatch(StockBatch stockBatch) {
        return stockRepository.update(stockBatch);
    }
    
    /**
     * Gets all stock batches for a specific item.
     * 
     * @param itemCode The item code
     * @return A list of stock batches for the item
     */
    public List<StockBatch> getStockBatchesByItemCode(String itemCode) {
        return stockRepository.findByItemCode(itemCode);
    }
    
    /**
     * Gets all stock batches of a specific type.
     * 
     * @param stockType The stock type
     * @return A list of stock batches of the specified type
     */
    public List<StockBatch> getStockBatchesByType(StockType stockType) {
        return stockRepository.findByStockType(stockType);
    }
    
    /**
     * Gets the total quantity of an item across all batches of a specific type.
     * 
     * @param itemCode The item code
     * @param stockType The stock type
     * @return The total quantity
     */
    public int getTotalQuantity(String itemCode, StockType stockType) {
        return stockRepository.getTotalQuantity(itemCode, stockType);
    }
    
    /**
     * Checks if there is sufficient stock of an item for a purchase.
     * 
     * @param itemCode The item code
     * @param quantity The quantity to check
     * @param stockType The stock type
     * @return true if sufficient stock is available, false otherwise
     */
    public boolean hasSufficientStock(String itemCode, int quantity, StockType stockType) {
        return getTotalQuantity(itemCode, stockType) >= quantity;
    }
    
    /**
     * Reduces stock for a purchase.
     * 
     * @param itemCode The item code
     * @param quantity The quantity to reduce
     * @param stockType The stock type
     * @return true if successful, false otherwise
     */
    public boolean reduceStock(String itemCode, int quantity, StockType stockType) {
        if (!hasSufficientStock(itemCode, quantity, stockType)) {
            return false;
        }
        
        int remainingQuantity = quantity;
        List<StockBatch> batches = getStockBatchesByItemCode(itemCode).stream()
                .filter(batch -> batch.getStockType() == stockType)
                .sorted(Comparator.comparing(StockBatch::getExpiryDate)
                        .thenComparing(StockBatch::getReceivedDate))
                .collect(Collectors.toList());
        
        for (StockBatch batch : batches) {
            if (remainingQuantity <= 0) {
                break;
            }
            
            int quantityToReduce = Math.min(remainingQuantity, batch.getQuantity());
            batch.decreaseQuantity(quantityToReduce);
            remainingQuantity -= quantityToReduce;
            
            stockRepository.update(batch);
        }
        
        return remainingQuantity == 0;
    }
    
    /**
     * Moves items from storage to shelf.
     * 
     * @param itemCode The item code
     * @param quantity The quantity to move
     * @return true if successful, false otherwise
     */
    public boolean moveFromStorageToShelf(String itemCode, int quantity) {
        if (!hasSufficientStock(itemCode, quantity, StockType.STORAGE)) {
            return false;
        }
        
        int remainingQuantity = quantity;
        List<StockBatch> storageBatches = getStockBatchesByItemCode(itemCode).stream()
                .filter(batch -> batch.getStockType() == StockType.STORAGE)
                .sorted(Comparator.comparing(StockBatch::getExpiryDate)
                        .thenComparing(StockBatch::getReceivedDate))
                .collect(Collectors.toList());
        
        for (StockBatch storageBatch : storageBatches) {
            if (remainingQuantity <= 0) {
                break;
            }
            
            int quantityToMove = Math.min(remainingQuantity, storageBatch.getQuantity());
            storageBatch.decreaseQuantity(quantityToMove);
            remainingQuantity -= quantityToMove;
            
            // Find or create a corresponding shelf batch
            Optional<StockBatch> shelfBatchOpt = getStockBatchesByItemCode(itemCode).stream()
                    .filter(batch -> batch.getStockType() == StockType.SHELF 
                            && batch.getExpiryDate().equals(storageBatch.getExpiryDate()))
                    .findFirst();
            
            if (shelfBatchOpt.isPresent()) {
                StockBatch shelfBatch = shelfBatchOpt.get();
                shelfBatch.increaseQuantity(quantityToMove);
                stockRepository.update(shelfBatch);
            } else {
                // Create a new shelf batch
                StockBatch newShelfBatch = new StockBatch(
                        "SB" + System.currentTimeMillis(),
                        storageBatch.getItem(),
                        quantityToMove,
                        LocalDate.now(),
                        storageBatch.getExpiryDate(),
                        StockType.SHELF
                );
                stockRepository.save(newShelfBatch);
            }
            
            stockRepository.update(storageBatch);
        }
        
        return remainingQuantity == 0;
    }
    
    /**
     * Gets items that need to be reordered (stock < 50).
     * 
     * @return A list of items that need to be reordered
     */
    public List<Item> getItemsToReorder() {
        return getAllItems().stream()
                .filter(item -> {
                    int totalQuantity = getTotalQuantity(item.getCode(), StockType.SHELF) 
                            + getTotalQuantity(item.getCode(), StockType.STORAGE)
                            + getTotalQuantity(item.getCode(), StockType.ONLINE);
                    return totalQuantity < 50;
                })
                .collect(Collectors.toList());
    }
}
