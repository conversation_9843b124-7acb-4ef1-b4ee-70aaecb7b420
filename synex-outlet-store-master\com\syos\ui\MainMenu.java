package com.syos.ui;

import com.syos.model.User;
import com.syos.service.BillingService;
import com.syos.service.InventoryService;
import com.syos.service.ReportService;
import com.syos.service.UserService;

import java.util.Scanner;

/**
 * Main menu for the SYOS application.
 */
public class MainMenu {
    
    private final Scanner scanner;
    private final BillingService billingService;
    private final InventoryService inventoryService;
    private final ReportService reportService;
    private final UserService userService;
    
    private User currentUser;
    
    private final BillingMenu billingMenu;
    private final InventoryMenu inventoryMenu;
    private final ReportMenu reportMenu;
    private final UserMenu userMenu;
    
    public MainMenu(BillingService billingService,
                    InventoryService inventoryService,
                    ReportService reportService,
                    UserService userService) {
        this.scanner = new Scanner(System.in);
        this.billingService = billingService;
        this.inventoryService = inventoryService;
        this.reportService = reportService;
        this.userService = userService;
        
        this.billingMenu = new BillingMenu(scanner, billingService, inventoryService, userService);
        this.inventoryMenu = new InventoryMenu(scanner, inventoryService);
        this.reportMenu = new ReportMenu(scanner, reportService);
        this.userMenu = new UserMenu(scanner, userService);
    }
    
    /**
     * Displays the main menu and handles user input.
     */
    public void displayMenu() {
        boolean exit = false;
        
        while (!exit) {
            System.out.println("\n===== SYNEX OUTLET STORE (SYOS) =====");
            System.out.println("1. Billing Operations");
            System.out.println("2. Inventory Management");
            System.out.println("3. Reports");
            System.out.println("4. User Management");
            System.out.println("5. Exit");
            System.out.print("Enter your choice: ");
            
            int choice;
            try {
                choice = Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Invalid input. Please enter a number.");
                continue;
            }
            
            switch (choice) {
                case 1:
                    billingMenu.displayMenu(currentUser);
                    break;
                case 2:
                    inventoryMenu.displayMenu();
                    break;
                case 3:
                    reportMenu.displayMenu();
                    break;
                case 4:
                    userMenu.displayMenu();
                    currentUser = userMenu.getCurrentUser();
                    break;
                case 5:
                    exit = true;
                    System.out.println("Thank you for using SYOS. Goodbye!");
                    break;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        }
    }
}
