package com.syos.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Represents a bill generated for a transaction.
 * Contains all the details needed for a receipt.
 */
public class Bill {
    private String billNumber;
    private Transaction transaction;
    private LocalDateTime generatedDateTime;
    
    public Bill() {
        this.generatedDateTime = LocalDateTime.now();
    }
    
    public Bill(String billNumber, Transaction transaction) {
        this.billNumber = billNumber;
        this.transaction = transaction;
        this.generatedDateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public String getBillNumber() {
        return billNumber;
    }
    
    public void setBillNumber(String billNumber) {
        this.billNumber = billNumber;
    }
    
    public Transaction getTransaction() {
        return transaction;
    }
    
    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }
    
    public LocalDateTime getGeneratedDateTime() {
        return generatedDateTime;
    }
    
    public void setGeneratedDateTime(LocalDateTime generatedDateTime) {
        this.generatedDateTime = generatedDateTime;
    }
    
    /**
     * Generates a formatted bill as a string.
     * 
     * @return The formatted bill
     */
    public String generateFormattedBill() {
        StringBuilder bill = new StringBuilder();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        // Header
        bill.append("===================================================\n");
        bill.append("                SYNEX OUTLET STORE                 \n");
        bill.append("===================================================\n");
        bill.append("Bill Number: ").append(billNumber).append("\n");
        bill.append("Date: ").append(generatedDateTime.format(formatter)).append("\n");
        bill.append("Transaction Type: ").append(transaction.getType()).append("\n");
        
        // Customer info if available
        if (transaction.getUser() != null) {
            bill.append("Customer: ").append(transaction.getUser().getFullName()).append("\n");
        }
        
        bill.append("---------------------------------------------------\n");
        bill.append(String.format("%-5s %-20s %-10s %-10s %-10s\n", 
                "No.", "Item", "Price", "Qty", "Subtotal"));
        bill.append("---------------------------------------------------\n");
        
        // Items
        List<CartItem> items = transaction.getItems();
        for (int i = 0; i < items.size(); i++) {
            CartItem item = items.get(i);
            bill.append(String.format("%-5d %-20s $%-9.2f %-10d $%-9.2f\n", 
                    i + 1, 
                    item.getItem().getName(), 
                    item.getItem().getPrice(), 
                    item.getQuantity(), 
                    item.getSubtotal()));
        }
        
        // Summary
        bill.append("---------------------------------------------------\n");
        bill.append(String.format("%-36s $%-9.2f\n", "Total:", transaction.getTotalAmount()));
        
        if (transaction.getDiscountAmount() > 0) {
            bill.append(String.format("%-36s $%-9.2f\n", "Discount:", transaction.getDiscountAmount()));
        }
        
        bill.append(String.format("%-36s $%-9.2f\n", "Final Amount:", transaction.getFinalAmount()));
        bill.append(String.format("%-36s $%-9.2f\n", "Cash Tendered:", transaction.getCashTendered()));
        bill.append(String.format("%-36s $%-9.2f\n", "Change:", transaction.getChange()));
        
        // Footer
        bill.append("===================================================\n");
        bill.append("          Thank you for shopping with us!          \n");
        bill.append("===================================================\n");
        
        return bill.toString();
    }
    
    @Override
    public String toString() {
        return "Bill{" +
                "billNumber='" + billNumber + '\'' +
                ", transaction=" + transaction +
                ", generatedDateTime=" + generatedDateTime +
                '}';
    }
}
