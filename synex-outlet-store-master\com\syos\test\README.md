# SYOS Test Suite

This package contains a comprehensive test suite for the Synex Outlet Store (SYOS) system. The tests are organized into the following categories:

## Test Structure

### Model Tests
Tests for the data model classes to ensure they function correctly:
- `ItemTest`: Tests for the Item class
- `StockBatchTest`: Tests for the StockBatch class
- `TransactionTest`: Tests for the Transaction class
- `BillTest`: Tests for the Bill class

### Repository Tests
Tests for the data persistence layer:
- `FileItemRepositoryTest`: Tests for the FileItemRepository class
- `FileStockRepositoryTest`: Tests for the FileStockRepository class

### Service Tests
Tests for the business logic layer:
- `InventoryServiceTest`: Tests for the InventoryService class
- `BillingServiceTest`: Tests for the BillingService class
- `ReportServiceTest`: Tests for the ReportService class
- `UserServiceTest`: Tests for the UserService class

### Integration Tests
Tests for complete workflows that span multiple components:
- `BillingWorkflowTest`: Tests the complete billing process
- `InventoryWorkflowTest`: Tests the inventory management process
- `ReportGenerationTest`: Tests the report generation functionality

### Test Utilities
- `TestDataUtil`: Utility class for creating test data
- `BaseTest`: Base class for all tests with common setup/teardown methods

## Running the Tests

To run all tests, execute the `SyosTestSuite` class. This will run all unit and integration tests.

```
java -cp <classpath> org.junit.runner.JUnitCore com.syos.test.SyosTestSuite
```

To run individual test classes, execute them directly:

```
java -cp <classpath> org.junit.runner.JUnitCore com.syos.test.model.ItemTest
```

## Test Coverage

The test suite covers all major functional requirements of the SYOS system:

1. **Billing System**
   - Item entry and code validation
   - Quantity input
   - Total calculation
   - Cash payment processing
   - Bill generation with all required details
   - Bill storage

2. **Inventory Management**
   - Stock reduction after checkout
   - Stock batch management with expiry dates
   - Moving items from storage to shelf based on expiry dates

3. **Online Sales**
   - User registration and authentication
   - Separate online inventory
   - Transaction type identification

4. **Reporting**
   - Daily sales reports
   - Reshelf reports
   - Reorder reports
   - Full stock reports
   - Bill reports

## Edge Cases Tested

The test suite includes tests for various edge cases:

- Insufficient stock for purchases
- Invalid payment amounts
- Expired stock handling
- Duplicate item codes
- Duplicate usernames
- Empty carts
- Zero or negative quantities
- Batch prioritization based on expiry dates

## Test Data Management

Test data is isolated from production data by using a separate test data directory. Each test starts with a clean environment and cleans up after itself to ensure test independence.
