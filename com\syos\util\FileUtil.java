package com.syos.util;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility class for file operations.
 */
public class FileUtil {
    
    /**
     * Ensures that a directory exists, creating it if necessary.
     * 
     * @param directoryPath The path of the directory
     * @return true if the directory exists or was created, false otherwise
     */
    public static boolean ensureDirectoryExists(String directoryPath) {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path)) {
            try {
                Files.createDirectories(path);
                return true;
            } catch (IOException e) {
                System.err.println("Failed to create directory: " + directoryPath);
                e.printStackTrace();
                return false;
            }
        }
        return true;
    }
    
    /**
     * Writes lines to a file.
     * 
     * @param filePath The path of the file
     * @param lines The lines to write
     * @return true if successful, false otherwise
     */
    public static boolean writeLinesToFile(String filePath, List<String> lines) {
        try {
            Path path = Paths.get(filePath);
            ensureDirectoryExists(path.getParent().toString());
            Files.write(path, lines);
            return true;
        } catch (IOException e) {
            System.err.println("Failed to write to file: " + filePath);
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Reads lines from a file.
     * 
     * @param filePath The path of the file
     * @return The lines read from the file, or an empty list if the file doesn't exist
     */
    public static List<String> readLinesFromFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                return Files.readAllLines(path);
            }
            return new ArrayList<>();
        } catch (IOException e) {
            System.err.println("Failed to read from file: " + filePath);
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    /**
     * Appends a line to a file.
     * 
     * @param filePath The path of the file
     * @param line The line to append
     * @return true if successful, false otherwise
     */
    public static boolean appendLineToFile(String filePath, String line) {
        try {
            Path path = Paths.get(filePath);
            ensureDirectoryExists(path.getParent().toString());
            
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath, true))) {
                writer.write(line);
                writer.newLine();
                return true;
            }
        } catch (IOException e) {
            System.err.println("Failed to append to file: " + filePath);
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Deletes a file.
     * 
     * @param filePath The path of the file
     * @return true if successful, false otherwise
     */
    public static boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                return true;
            }
            return false;
        } catch (IOException e) {
            System.err.println("Failed to delete file: " + filePath);
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Checks if a file exists.
     * 
     * @param filePath The path of the file
     * @return true if the file exists, false otherwise
     */
    public static boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }
}
