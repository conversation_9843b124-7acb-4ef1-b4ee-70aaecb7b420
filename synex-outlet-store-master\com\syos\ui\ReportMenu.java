package com.syos.ui;

import com.syos.service.ReportService;
import com.syos.util.DateUtil;

import java.time.LocalDate;
import java.util.Scanner;

/**
 * Menu for report generation operations.
 */
public class ReportMenu {
    
    private final Scanner scanner;
    private final ReportService reportService;
    
    public ReportMenu(Scanner scanner, ReportService reportService) {
        this.scanner = scanner;
        this.reportService = reportService;
    }
    
    /**
     * Displays the report menu and handles user input.
     */
    public void displayMenu() {
        boolean back = false;
        
        while (!back) {
            System.out.println("\n===== Reports =====");
            System.out.println("1. Daily Sales Report");
            System.out.println("2. Reshelf Report");
            System.out.println("3. Reorder Report");
            System.out.println("4. Full Stock Report");
            System.out.println("5. Bill Report");
            System.out.println("6. Back to Main Menu");
            System.out.print("Enter your choice: ");
            
            int choice;
            try {
                choice = Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Invalid input. Please enter a number.");
                continue;
            }
            
            switch (choice) {
                case 1:
                    generateDailySalesReport();
                    break;
                case 2:
                    generateReshelfReport();
                    break;
                case 3:
                    generateReorderReport();
                    break;
                case 4:
                    generateFullStockReport();
                    break;
                case 5:
                    generateBillReport();
                    break;
                case 6:
                    back = true;
                    break;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        }
    }
    
    /**
     * Generates a daily sales report.
     */
    private void generateDailySalesReport() {
        System.out.println("\n===== Daily Sales Report =====");
        
        System.out.print("Enter date (yyyy-MM-dd) or leave blank for today: ");
        String dateStr = scanner.nextLine().trim();
        
        LocalDate date;
        if (dateStr.isEmpty()) {
            date = LocalDate.now();
        } else {
            date = DateUtil.parseDate(dateStr);
            if (date == null) {
                System.out.println("Invalid date format. Report not generated.");
                return;
            }
        }
        
        String reportPath = reportService.generateDailySalesReport(date);
        System.out.println("Report generated successfully: " + reportPath);
        
        // Display report content
        System.out.println("\nReport Preview:");
        for (String line : com.syos.util.FileUtil.readLinesFromFile(reportPath)) {
            System.out.println(line);
        }
    }
    
    /**
     * Generates a report of items to be reshelved.
     */
    private void generateReshelfReport() {
        System.out.println("\n===== Reshelf Report =====");
        
        String reportPath = reportService.generateReshelfReport();
        System.out.println("Report generated successfully: " + reportPath);
        
        // Display report content
        System.out.println("\nReport Preview:");
        for (String line : com.syos.util.FileUtil.readLinesFromFile(reportPath)) {
            System.out.println(line);
        }
    }
    
    /**
     * Generates a report of items to be reordered.
     */
    private void generateReorderReport() {
        System.out.println("\n===== Reorder Report =====");
        
        String reportPath = reportService.generateReorderReport();
        System.out.println("Report generated successfully: " + reportPath);
        
        // Display report content
        System.out.println("\nReport Preview:");
        for (String line : com.syos.util.FileUtil.readLinesFromFile(reportPath)) {
            System.out.println(line);
        }
    }
    
    /**
     * Generates a full stock report.
     */
    private void generateFullStockReport() {
        System.out.println("\n===== Full Stock Report =====");
        
        String reportPath = reportService.generateFullStockReport();
        System.out.println("Report generated successfully: " + reportPath);
        
        // Display report content
        System.out.println("\nReport Preview:");
        for (String line : com.syos.util.FileUtil.readLinesFromFile(reportPath)) {
            System.out.println(line);
        }
    }
    
    /**
     * Generates a bill report.
     */
    private void generateBillReport() {
        System.out.println("\n===== Bill Report =====");
        
        String reportPath = reportService.generateBillReport();
        System.out.println("Report generated successfully: " + reportPath);
        
        // Display report content
        System.out.println("\nReport Preview:");
        for (String line : com.syos.util.FileUtil.readLinesFromFile(reportPath)) {
            System.out.println(line);
        }
    }
}
