package com.syos;

import com.syos.model.*;
import com.syos.repository.*;
import com.syos.repository.impl.*;
import com.syos.service.*;
import com.syos.ui.MainMenu;
import com.syos.util.DateUtil;
import com.syos.util.FileUtil;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Main application class for the Synex Outlet Store (SYOS) system.
 */
public class SyosApplication {
    
    private static final String DATA_DIRECTORY = "data";
    
    public static void main(String[] args) {
        System.out.println("Starting Synex Outlet Store (SYOS) System...");
        
        // Ensure data directory exists
        FileUtil.ensureDirectoryExists(DATA_DIRECTORY);
        
        // Initialize repositories
        ItemRepository itemRepository = new FileItemRepository();
        StockRepository stockRepository = new FileStockRepository();
        UserRepository userRepository = new FileUserRepository();
        TransactionRepository transactionRepository = new FileTransactionRepository();
        BillRepository billRepository = new FileBillRepository();
        
        // Initialize services
        InventoryService inventoryService = new InventoryService(itemRepository, stockRepository);
        UserService userService = new UserService(userRepository);
        BillingService billingService = new BillingService(
                itemRepository, transactionRepository, billRepository, inventoryService);
        ReportService reportService = new ReportService(
                itemRepository, stockRepository, transactionRepository, billRepository, inventoryService);
        
        // Load sample data if needed
        loadSampleData(itemRepository, stockRepository, userRepository);
        
        // Start the UI
        MainMenu mainMenu = new MainMenu(billingService, inventoryService, reportService, userService);
        mainMenu.displayMenu();
    }
    
    /**
     * Loads sample data for testing.
     * 
     * @param itemRepository The item repository
     * @param stockRepository The stock repository
     * @param userRepository The user repository
     */
    private static void loadSampleData(
            ItemRepository itemRepository,
            StockRepository stockRepository,
            UserRepository userRepository) {
        
        // Check if data already exists
        if (!itemRepository.findAll().isEmpty()) {
            return; // Data already loaded
        }
        
        System.out.println("Loading sample data...");
        
        // Sample items
        List<Item> items = new ArrayList<>();
        items.add(new Item("I001", "Rice (1kg)", 2.50, "Groceries"));
        items.add(new Item("I002", "Flour (1kg)", 1.80, "Groceries"));
        items.add(new Item("I003", "Sugar (1kg)", 1.50, "Groceries"));
        items.add(new Item("I004", "Milk (1L)", 1.20, "Dairy"));
        items.add(new Item("I005", "Eggs (12)", 2.00, "Dairy"));
        items.add(new Item("I006", "Bread", 1.00, "Bakery"));
        items.add(new Item("I007", "Chicken (1kg)", 5.00, "Meat"));
        items.add(new Item("I008", "Beef (1kg)", 7.50, "Meat"));
        items.add(new Item("I009", "Apples (1kg)", 2.20, "Fruits"));
        items.add(new Item("I010", "Bananas (1kg)", 1.80, "Fruits"));
        
        // Save items
        for (Item item : items) {
            itemRepository.save(item);
        }
        
        // Sample stock batches
        LocalDate today = LocalDate.now();
        
        // Shelf stock
        for (Item item : items) {
            StockBatch shelfBatch = new StockBatch(
                    "SB" + item.getCode(),
                    item,
                    20,
                    today.minusDays(5),
                    today.plusMonths(6),
                    StockType.SHELF
            );
            stockRepository.save(shelfBatch);
        }
        
        // Storage stock
        for (Item item : items) {
            StockBatch storageBatch = new StockBatch(
                    "ST" + item.getCode(),
                    item,
                    80,
                    today.minusDays(10),
                    today.plusMonths(8),
                    StockType.STORAGE
            );
            stockRepository.save(storageBatch);
        }
        
        // Online stock
        for (Item item : items) {
            StockBatch onlineBatch = new StockBatch(
                    "ON" + item.getCode(),
                    item,
                    50,
                    today.minusDays(7),
                    today.plusMonths(7),
                    StockType.ONLINE
            );
            stockRepository.save(onlineBatch);
        }
        
        // Sample users
        User admin = new User(
                "U001",
                "admin",
                "admin123",
                "System Administrator",
                "<EMAIL>",
                "123 Admin St",
                "123-456-7890",
                false
        );
        
        User onlineCustomer = new User(
                "U002",
                "customer",
                "customer123",
                "John Doe",
                "<EMAIL>",
                "456 Customer Ave",
                "987-654-3210",
                true
        );
        
        userRepository.save(admin);
        userRepository.save(onlineCustomer);
        
        System.out.println("Sample data loaded successfully.");
    }
}
