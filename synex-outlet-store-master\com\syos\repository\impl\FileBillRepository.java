package com.syos.repository.impl;

import com.syos.model.Bill;
import com.syos.model.Transaction;
import com.syos.repository.BillRepository;
import com.syos.repository.TransactionRepository;
import com.syos.util.DateUtil;
import com.syos.util.FileUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * File-based implementation of the BillRepository interface.
 */
public class FileBillRepository implements BillRepository {
    
    private static final String DATA_DIRECTORY = "data";
    private static final String BILLS_FILE = DATA_DIRECTORY + "/bills.csv";
    private static final String BILLS_CONTENT_DIRECTORY = DATA_DIRECTORY + "/bills";
    private static final String DELIMITER = ",";
    
    private final TransactionRepository transactionRepository = new FileTransactionRepository();
    
    /**
     * Converts a Bill object to a CSV line.
     * 
     * @param bill The bill to convert
     * @return The CSV line
     */
    private String billToCsvLine(Bill bill) {
        return String.join(DELIMITER,
                bill.getBillNumber(),
                bill.getTransaction().getTransactionId(),
                DateUtil.formatDateTime(bill.getGeneratedDateTime()));
    }
    
    /**
     * Converts a CSV line to a Bill object.
     * 
     * @param line The CSV line
     * @return The Bill object, or null if conversion fails
     */
    private Bill csvLineToBill(String line) {
        String[] parts = line.split(DELIMITER);
        if (parts.length >= 3) {
            String billNumber = parts[0];
            String transactionId = parts[1];
            LocalDateTime generatedDateTime = DateUtil.parseDateTime(parts[2]);
            
            Optional<Transaction> transactionOpt = transactionRepository.findById(transactionId);
            if (transactionOpt.isPresent()) {
                Bill bill = new Bill(billNumber, transactionOpt.get());
                bill.setGeneratedDateTime(generatedDateTime);
                return bill;
            }
        }
        return null;
    }
    
    @Override
    public Bill save(Bill bill) {
        List<Bill> bills = findAll();
        
        // Check if bill with the same number already exists
        Optional<Bill> existingBill = bills.stream()
                .filter(b -> b.getBillNumber().equals(bill.getBillNumber()))
                .findFirst();
        
        if (existingBill.isPresent()) {
            // Update existing bill
            return update(bill);
        } else {
            // Add new bill
            List<String> lines = bills.stream()
                    .map(this::billToCsvLine)
                    .collect(Collectors.toList());
            
            lines.add(billToCsvLine(bill));
            
            FileUtil.writeLinesToFile(BILLS_FILE, lines);
            
            // Save bill content to a separate file
            saveBillContent(bill);
            
            return bill;
        }
    }
    
    /**
     * Saves the formatted bill content to a file.
     * 
     * @param bill The bill
     */
    private void saveBillContent(Bill bill) {
        FileUtil.ensureDirectoryExists(BILLS_CONTENT_DIRECTORY);
        String billContentFile = BILLS_CONTENT_DIRECTORY + "/" + bill.getBillNumber() + ".txt";
        String formattedBill = bill.generateFormattedBill();
        FileUtil.writeLinesToFile(billContentFile, List.of(formattedBill));
    }
    
    @Override
    public Optional<Bill> findByNumber(String billNumber) {
        return findAll().stream()
                .filter(bill -> bill.getBillNumber().equals(billNumber))
                .findFirst();
    }
    
    @Override
    public List<Bill> findByTransactionId(String transactionId) {
        return findAll().stream()
                .filter(bill -> bill.getTransaction().getTransactionId().equals(transactionId))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Bill> findByDate(LocalDate date) {
        return findAll().stream()
                .filter(bill -> bill.getGeneratedDateTime().toLocalDate().equals(date))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Bill> findAll() {
        List<String> lines = FileUtil.readLinesFromFile(BILLS_FILE);
        List<Bill> bills = new ArrayList<>();
        
        for (String line : lines) {
            Bill bill = csvLineToBill(line);
            if (bill != null) {
                bills.add(bill);
            }
        }
        
        return bills;
    }
    
    @Override
    public boolean delete(String billNumber) {
        List<Bill> bills = findAll();
        List<Bill> updatedBills = bills.stream()
                .filter(bill -> !bill.getBillNumber().equals(billNumber))
                .collect(Collectors.toList());
        
        if (updatedBills.size() < bills.size()) {
            List<String> lines = updatedBills.stream()
                    .map(this::billToCsvLine)
                    .collect(Collectors.toList());
            
            boolean billDeleted = FileUtil.writeLinesToFile(BILLS_FILE, lines);
            
            // Delete bill content file
            String billContentFile = BILLS_CONTENT_DIRECTORY + "/" + billNumber + ".txt";
            boolean contentDeleted = FileUtil.deleteFile(billContentFile);
            
            return billDeleted && contentDeleted;
        }
        
        return false;
    }
    
    @Override
    public String getNextBillNumber() {
        List<Bill> bills = findAll();
        int maxId = 0;
        
        for (Bill bill : bills) {
            try {
                int id = Integer.parseInt(bill.getBillNumber().substring(1));
                if (id > maxId) {
                    maxId = id;
                }
            } catch (NumberFormatException | IndexOutOfBoundsException e) {
                // Ignore invalid IDs
            }
        }
        
        return "B" + String.format("%04d", maxId + 1);
    }
    
    /**
     * Updates an existing bill.
     * 
     * @param bill The bill to update
     * @return The updated bill
     */
    private Bill update(Bill bill) {
        delete(bill.getBillNumber());
        return save(bill);
    }
}
