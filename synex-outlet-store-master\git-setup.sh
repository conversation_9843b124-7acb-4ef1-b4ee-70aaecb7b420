#!/bin/bash

# 1. Initialize the local repository
echo "Initializing Git repository..."
git init

# 2. Add all files (excluding those in .gitignore)
echo "Adding files to Git..."
git add .

# 3. Make an initial commit
echo "Making initial commit..."
git commit -m "Initial commit: Synex Outlet Store (SYOS) system implementation"

# 4. Add the remote repository
echo "Adding remote repository..."
git remote add origin https://github.com/Hemini03/synex-outlet-store.git

# 5. Push to the main branch
echo "Pushing to main branch..."
git push -u origin main

echo "Git setup complete!"
