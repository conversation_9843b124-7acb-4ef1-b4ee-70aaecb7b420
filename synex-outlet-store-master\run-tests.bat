@echo off
echo Setting up test environment...
mkdir bin 2>nul
mkdir data 2>nul
mkdir reports 2>nul
mkdir lib 2>nul

echo Compiling main classes...
javac -d bin com\syos\model\*.java com\syos\repository\*.java com\syos\repository\impl\*.java com\syos\service\*.java com\syos\util\*.java com\syos\ui\*.java com\syos\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Main class compilation failed!
    exit /b %ERRORLEVEL%
)

echo Compiling test classes...
javac -d bin -cp "bin;lib\junit-4.13.2.jar;lib\mockito-core-3.12.4.jar;lib\hamcrest-core-1.3.jar;lib\byte-buddy-1.11.13.jar" com\syos\test\util\*.java com\syos\test\base\*.java com\syos\test\model\*.java com\syos\test\repository\*.java com\syos\test\service\*.java com\syos\test\integration\*.java com\syos\test\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Test class compilation failed!
    exit /b %ERRORLEVEL%
)

echo Running tests...
java -cp "bin;lib\junit-4.13.2.jar;lib\mockito-core-3.12.4.jar;lib\hamcrest-core-1.3.jar;lib\byte-buddy-1.11.13.jar" org.junit.runner.JUnitCore com.syos.test.SyosTestSuite
